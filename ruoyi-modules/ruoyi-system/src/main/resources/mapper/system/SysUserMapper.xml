<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="deptId"       column="dept_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="workbenchRoleType"  column="workbench_role_type"  />
        <result property="selectionManagement"  column="selection_management"  />
        <result property="sex"          column="sex"          />
        <result property="dataScope"          column="data_scope"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
        <association property="dept"    column="dept_id" javaType="com.ruoyi.system.api.domain.entity.SysDept" resultMap="deptResult" />
        <collection  property="roles"   javaType="java.util.List"           resultMap="RoleResult" />
    </resultMap>
	
    <resultMap id="deptResult" type="com.ruoyi.system.api.domain.entity.SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
        <result property="ancestorName"    column="ancestor_name" />
    </resultMap>
	
    <resultMap id="RoleResult" type="com.ruoyi.system.api.domain.entity.SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="status"       column="role_status"    />
    </resultMap>
	
	<sql id="selectUserVo">
        select u.user_id, u.dept_id,u.workbench_role_type, u.selection_management, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.data_scope,u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>
    
    <select id="selectUserList" parameterType="com.ruoyi.system.api.domain.entity.SysUser" resultMap="SysUserResult">
		SELECT u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.PASSWORD, u.sex, u.STATUS,
		       u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
		       d.dept_name, d.ancestor_name, d.leader
		FROM
			sys_user u
				LEFT JOIN (
					SELECT
						d1.dept_id, d1.dept_name, d1.ancestors, d1.leader,
						IF(d1.ancestors = '0', d1.dept_name, d2.dept_name) AS ancestor_name
					FROM
						sys_dept d1
						LEFT JOIN
							sys_dept d2 ON d2.dept_id = CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(d1.ancestors, ',', 2), ',', -1) AS UNSIGNED)
					WHERE d1.del_flag != 2
					)d ON u.dept_id = d.dept_id
		WHERE
			u.del_flag = '0'
			<if test="userId != null and userId != 0">
				AND u.user_id = #{userId}
			</if>
			<if test="userName != null and userName != ''">
				AND u.user_name like concat('%', #{userName}, '%')
			</if>
			<if test="status != null and status != ''">
				AND u.status = #{status}
			</if>
			<if test="phonenumber != null and phonenumber != ''">
				AND u.phonenumber like concat('%', #{phonenumber}, '%')
			</if>
			<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
			</if>
			<if test="deptId != null and deptId != 0">
				AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
			</if>
			<if test="roleId != null and roleId != 0">
				AND u.user_id in (select sur.user_id from sys_user_role sur where sur.role_id=#{roleId})
			</if>
			<!-- 数据范围过滤 -->
			${params.dataScope}
		order by
		    u.status asc,
		    u.create_time desc
	</select>
	
	<select id="selectAllocatedList" parameterType="com.ruoyi.system.api.domain.entity.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUnallocatedList" parameterType="com.ruoyi.system.api.domain.entity.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName}
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	
	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_name = #{userName} and del_flag = 0 limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = 0 limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = 0 limit 1
	</select>
	<select id="selectUserListNoPage" resultMap="SysUserResult">
		SELECT u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.PASSWORD, u.sex, u.STATUS, u.del_flag,
		       u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
		       d.dept_name, d.ancestor_name, d.leader,
		       r.role_name, r.role_id
		FROM
			sys_user u
				LEFT JOIN (
							SELECT
							   d1.dept_id,d1.dept_name,d1.ancestors,d1.leader,
							   IF(d1.ancestors = '0', d1.dept_name, d2.dept_name) AS ancestor_name
						   	FROM
							   sys_dept d1
								   LEFT JOIN
							   sys_dept d2 ON d2.dept_id = CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(d1.ancestors, ',', 2), ',', -1) AS UNSIGNED)
						   	WHERE d1.del_flag != 2
				)d ON u.dept_id = d.dept_id
				LEFT JOIN sys_user_role sur ON u.user_id = sur.user_id
				LEFT JOIN sys_role r ON sur.role_id = r.role_id
		WHERE
			u.del_flag = '0'
			<if test="dto.userId != null and dto.userId.size() != 0 ">
				and u.user_id in
					<foreach collection="dto.userId" item="userId" open="(" separator="," close=")">
						#{userId}
					</foreach>
			</if>
			<if test="dto.userName != null and dto.userName != '' ">
				and u.user_name like concat('%', #{dto.userName}, '%')
			</if>
			<if test="dto.deptId != null and dto.deptId != 0">
				AND (u.dept_id = #{dto.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{dto.deptId}, ancestors) ))
			</if>
	</select>
	<select id="getUserLevel" resultMap="SysUserResult">
		SELECT
			u.user_id,u.user_name,u.nick_name,u.phonenumber,u.sex,u.avatar
		FROM
			sys_user u
		<where>
			<if test="userId != null">
					u.dept_id IN (
					SELECT
						d.dept_id
					FROM
						sys_dept d
					WHERE
							d.ancestors LIKE CONCAT(
								(
									SELECT
										CONCAT( d.ancestors, ',', d.dept_id ,',' )
									FROM
										sys_dept d
									WHERE
										d.dept_id = ( SELECT u.dept_id FROM sys_user u WHERE u.user_id = #{userId} )
								),
								'%'
							)
							OR d.ancestors = (
									SELECT
										CONCAT( d.ancestors, ',', d.dept_id )
									FROM
										sys_dept d
									WHERE
										d.dept_id = ( SELECT u.dept_id FROM sys_user u WHERE u.user_id = #{userId} )
							)
							OR d.dept_id = (SELECT u.dept_id FROM sys_user u WHERE u.user_id = #{userId})
					)
					OR u.user_id = #{userId}
			</if>
		</where>
	</select>
	<select id="customerServiceSelect" resultType="com.ruoyi.common.core.domain.vo.UserVO">
		SELECT
		    u.user_id AS id,
		    u.user_name AS name,
		    u.phonenumber
		FROM
			sys_user u
		<where>
			u.del_flag = '0'
			<if test="dto.userId != null and dto.userId.size() != 0 ">
				AND u.user_id IN
				<foreach collection="dto.userId" item="userId" open="(" separator="," close=")">
					#{userId}
				</foreach>
			</if>

			<if test="dto.userName != null and dto.userName != '' ">
				AND u.user_name LIKE CONCAT('%', #{dto.userName}, '%')
			</if>
		</where>
	</select>

	<insert id="insertUser" parameterType="com.ruoyi.system.api.domain.entity.SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="dataScope != null and dataScope != ''">data_scope,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="dataScope != null and dataScope != ''">#{dataScope},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateUser" parameterType="com.ruoyi.system.api.domain.entity.SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="workbenchRoleType != null ">workbench_role_type = #{workbenchRoleType},</if>
 			<if test="selectionManagement != null ">selection_management = #{selectionManagement},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="dataScope != null and dataScope != ''">data_scope = #{dataScope},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="com.ruoyi.system.api.domain.entity.SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="com.ruoyi.system.api.domain.entity.SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="com.ruoyi.system.api.domain.entity.SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>
	<delete id="deleteUserById" parameterType="Long">
		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>
	
</mapper> 