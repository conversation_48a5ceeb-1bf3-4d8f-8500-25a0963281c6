package com.ruoyi.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.dto.system.WorkbenchRoleDTO;
import com.ruoyi.system.api.domain.entity.SysRole;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class SysUserController extends BaseController {
    private final ISysUserService userService;

    private final ISysRoleService roleService;

    private final ISysPostService postService;

    private final ISysPermissionService permissionService;

    private final ISysConfigService configService;

    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    // @RequiresPermissions("system:user:list")
    @GetMapping("/userList")
    public AjaxResult userList(SysUser user) {
        if (StringUtils.isNull(user.getStatus())){
            user.setStatus(StatusEnum.ENABLED.getCode().toString());
        }
        List<SysUser> list = userService.selectUserList(user);
        List<SysUserVO> sysUserVOS = BeanUtil.copyToList(list, SysUserVO.class);
        return AjaxResult.success(sysUserVOS);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplate")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info/{username}")
    public R<LoginUser> info(@PathVariable("username") String username) {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser.getUserId());
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return R.ok(sysUserVo);
    }

    /**
     * 注册用户信息
     */
    @InnerAuth
    @PostMapping("/register")
    public R<Boolean> register(@RequestBody SysUser sysUser) {
        String username = sysUser.getUserName();
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
            return R.fail("当前系统没有开启注册功能！");
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(username))) {
            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
        }
        return R.ok(userService.registerUser(sysUser));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        Long userId = SecurityUtils.getUserId();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(userId);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(userId);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", userService.selectUserById(userId));
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        SysUser odlUser = userService.selectUserByUserName(user.getUserName());
        if (odlUser != null && !odlUser.getUserId().equals(user.getUserId())) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, SecurityUtils.getUserId())) {
            return AjaxResult.error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @RequiresPermissions("system:user:query")
    @GetMapping("/authRole/{userId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds, String dataScope) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds, dataScope);
        return success();
    }

    /**
     * 修改工作台角色
     * @return
     */
    @RequiresPermissions("system:user:editWorkbenchRole")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/editWorkbenchRole")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public AjaxResult editWorkbenchRole(@RequestBody @Validated WorkbenchRoleDTO dto) {
        SysUser sysUser= new SysUser();
        sysUser.setUserId(dto.getUserId());
        userService.checkUserAllowed(sysUser);
        userService.checkUserDataScope(dto.getUserId());
        userService.editWorkbenchRole(dto);
        return success();
    }

    /**
     * 记录用户登录信息
     */
    @InnerAuth
    @PostMapping("recordLoginInfo")
    public R<Boolean> recordLoginInfo(@RequestBody SysUser sysUser) {
        sysUser.setUpdateBy(SecurityUtils.getUsername());
        return R.ok(userService.updateUserProfile(sysUser) > 0);
    }


    /**
     * 获取用户列表 不分页 内部请求
     */
    @InnerAuth
    @PostMapping("/listNoPage")
    public R<List<SysUser>> listNoPage(@RequestBody SysUserListDTO dto) {
        return R.ok(userService.selectUserListNoPage(dto));
    }

    /**
     * 根据用户ID查询用户完整信息（内部请求）
     * 用于获取最新的用户权限信息，解决缓存数据不实时的问题
     *
     * @param userId 用户ID
     * @return 用户完整信息，包含selection_management等所有字段
     */
    @InnerAuth
    @GetMapping("/detail/{userId}")
    public R<SysUser> selectUserById(@PathVariable("userId") Long userId) {
        SysUser sysUser = userService.selectUserById(userId);
        return R.ok(sysUser);
    }

    /**
     * 获取当前用户以及他下级部门的用户信息
     */
    @InnerAuth
    @GetMapping("/get-user-level")
    public R<List<SysUserVO>> getUserLevel() {
        return R.ok(userService.getUserLevel());
    }

    /**
     * 客服数据-中文部客服数据
     */
    @GetMapping("/chinese-customer-service-data")
    @InnerAuth
    public R<List<ChineseCustomerServiceDataVO>> selectChineseCustomerServiceData() {
        List<ChineseCustomerServiceDataVO> chineseCustomerServiceDataVOS = userService.selectChineseCustomerServiceData();
        return R.ok(chineseCustomerServiceDataVOS);
    }

    /**
     * 客服数据-英文部客服数据
     */
    @GetMapping("/english-customer-service-data")
    @InnerAuth
    public R<List<EnglishCustomerServiceDataVO>> selectEnglishCustomerServiceData() {
        List<EnglishCustomerServiceDataVO> englishCustomerServiceDataVOS = userService.selectEnglishCustomerServiceData();
        return R.ok(englishCustomerServiceDataVOS);
    }

    /**
     * 获取客服下拉框
     */
    @PostMapping("/customer-service-select")
    public R<List<UserVO>> customerServiceSelect(@RequestBody SysUserListDTO dto) {
        List<UserVO> customerServiceSelectVOS = userService.customerServiceSelect(dto);
        return R.ok(customerServiceSelectVOS);
    }
}
