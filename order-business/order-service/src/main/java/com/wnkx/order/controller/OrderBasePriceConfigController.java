package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigChangelogVO;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigVO;
import com.wnkx.order.service.OrderBasePriceConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/price/config")
@Api(value = "支付基础信息配置服务", tags = "支付基础信息配置服务")
@RequiredArgsConstructor
public class OrderBasePriceConfigController {

    private final OrderBasePriceConfigService orderBasePriceConfigService;

    @PostMapping("/service")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "修改当前服务费配置")
    @Log(title = "修改当前服务费配置", businessType = BusinessType.UPDATE)
    public R<String> changeServicePriceConfig(@RequestBody @Validated OrderBasePriceConfigVO orderBasePriceConfigVO) {
        orderBasePriceConfigService.changeConfigByType(orderBasePriceConfigVO, OrderConstant.TYPE_SERVICE_PRICE);
        return R.ok();
    }

    @GetMapping("/service")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "获取当前服务费配置")
    public R<OrderBasePriceConfigVO> getServicePriceConfig() {
        return R.ok(orderBasePriceConfigService.getCurrentConfigByte(OrderConstant.TYPE_SERVICE_PRICE));
    }

    @GetMapping("/service/active")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "获取当前生效服务费配置")
    public R<OrderBasePriceConfigVO> getServicePriceConfigActive() {
        return R.ok(orderBasePriceConfigService.getCurrentConfigByteActive(OrderConstant.TYPE_SERVICE_PRICE));
    }

    @GetMapping("/service/log")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取当前服务费配置修改记录")
    public R<List<OrderBasePriceConfigChangelogVO>> getServicePriceConfigLog() {
        return R.ok(orderBasePriceConfigService.getConfigChangeLogList(OrderConstant.TYPE_SERVICE_PRICE), "ok");
    }
}
