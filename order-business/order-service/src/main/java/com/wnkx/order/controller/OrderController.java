package com.wnkx.order.controller;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.MemberListDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.MemberAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.CheckWechatVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountSelectVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.annotations.OrderPermissions;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.annotations.OrderVideoOperate;
import com.wnkx.order.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@RestController
@RequestMapping("/order")
@Api(value = "订单服务", tags = "订单服务")
@RequiredArgsConstructor
@Validated
public class OrderController extends BaseController {
    private final IOrderService orderService;
    private final VideoCartService videoCartService;
    private final IOrderVideoService orderVideoService;
    private final IOrderMemberService orderMemberService;
    private final IPayeeAccountConfigService payeeAccountConfigService;
    private final OrderVideoUploadLinkService orderVideoUploadLinkService;


    /**
     * 创建订单
     */
    @PostMapping("/create-order")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "创建订单")
    @Log(title = "创建订单", businessType = BusinessType.INSERT)
    @MemberAuth
    @Validated(CommonValidatedGroup.SaveValidatedGroup.class)
    public R<CreateOrderVO> createOrder(@Valid @RequestBody List<OrderVideoDTO> orderVideoDTOS) {
        CreateOrderVO createOrderVO = orderService.createOrder(orderVideoDTOS);
        return R.ok(createOrderVO);
    }

    /**
     * 获取用户账号创建订单数量
     */
    @GetMapping("/get-user-account-create-order-count")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取用户账号创建订单数量")
    public R<Long> getUserAccountCreateOrderCount() {
        Long count = orderService.getUserAccountCreateOrderCount();
        return R.ok(count);
    }

    /**
     * 查询订单列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查询订单列表", response = PageInfo.class)
    @RequiresPermissions("order:manage:list")
    public R<PageInfo<OrderListVO>> selectOrderListByCondition(@Validated OrderListDTO orderListDTO) {
        orderListDTO.setStartPage(StatusTypeEnum.YES.getCode());
        if (CollUtil.isNotEmpty(orderListDTO.getPayType())){
            orderListDTO.setIsFilterClose(StatusTypeEnum.YES.getCode());
        }
        List<OrderListVO> list = orderService.selectOrderListByCondition(orderListDTO);
        return R.ok(toPage(list));
    }
    @GetMapping("/accountOrderList")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查询登录账号订单列表", response = PageInfo.class)
    public R<PageInfo<OrderVideoVO>> accountOrderList() {
        List<OrderVideoVO> orderVideoVOS = orderVideoService.selectAccountOrderList();
        return R.ok(toPage(orderVideoVOS));
    }

    /**
     * 获取订单列表视频订单统计数量
     */
    @GetMapping("/video/statistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取订单列表视频订单统计数量")
    @RequiresPermissions("order:manage:list")
    public R<Integer> videoStatistics(@Validated OrderListDTO orderListDTO) {
        return R.ok(orderService.videoStatistics(orderListDTO));
    }

    @GetMapping("/member/list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isNoMock = false)
    @ApiOperation(value = "查询会员订单列表", response = PageInfo.class)
    public R<PageInfo<OrderMemberVO>> selectOrderMemberList(OrderMemberListDTO dto) {
        return R.ok(toPage(orderMemberService.getOrderMemberList(dto)));
    }

    @GetMapping("/member/getMemberUnPay")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isNoMock = false)
    @ApiOperation(value = "查询会员未支付数据", response = MemberUnPayVO.class)
    public R<MemberUnPayVO> getMemberUnPay() {
        return R.ok(orderMemberService.getMemberUnPay());
    }

    @GetMapping("/backend/member/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询会员订单列表（运营端）", response = PageInfo.class)
    @RequiresPermissions("order:vip:list")
    public R<PageInfo<OrderMemberVO>> selectOrderMemberListBackend(OrderMemberListDTO dto) {
        if (CollUtil.isNotEmpty(dto.getPayTypes())){
            dto.setIsFilterClose(StatusTypeEnum.YES.getCode());
        }
        List<OrderMemberVO> backendOrderMemberList = orderMemberService.getBackendOrderMemberList(dto);
        MemberListDto memberListDto = new MemberListDto();
        if (dto.getPayTimeBegin() != null && dto.getPayTimeEnd() != null) {
            BeanUtils.copyProperties(dto, memberListDto);
            memberListDto.setTotalAmount(orderMemberService.getBackendOrderMemberListByPayTime(dto));
        }
        return R.ok(toPage(backendOrderMemberList, memberListDto));
    }

    @PostMapping("/backend/export/member/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "导出会员订单列表（运营端）")
    @Log(title = "导出会员订单列表", businessType = BusinessType.EXPORT)
    @RequiresPermissions("order:vip:export")
    public void exportOrderMemberListBackend(OrderMemberListDTO dto, HttpServletResponse response) {
        if (CollUtil.isNotEmpty(dto.getPayTypes())){
            dto.setIsFilterClose(StatusTypeEnum.YES.getCode());
        }
        List<OrderMemberVO> orderMemberList = orderMemberService.getBackendOrderMemberList(dto);
        List<OrderMemberExportVO> orderMemberExportList = orderMemberService.getBackendOrderMemberListExport(orderMemberList);
        ExcelUtil<OrderMemberExportVO> util = new ExcelUtil<>(OrderMemberExportVO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "会员订单列表导出");
        util.exportExcel(response, orderMemberExportList, "会员订单列表导出");
    }

    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping("/member/create-order")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isNoMock = false)
    @ApiOperation(value = "创建会员订单")
    public R<OrderMemberVO> createMemberOrder(@RequestBody @Valid CreateOrderMemberDTO dto) {
        return R.ok(orderMemberService.createOrder(dto));
    }

    /**
     * 修改视频订单_获取视频详细信息
     *
     * @param id 视频订单的主键
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "修改视频订单_获取视频详细信息", response = OrderVideoVO.class)
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @OrderPermissions(orderId = "#id")
    public R<OrderVideoVO> getOrderVideoInfo(@ApiParam("视频订单的主键") @PathVariable("id") Long id) {
        return R.ok(orderService.getOrderVideoInfo(id));
    }

    /**
     * 修改视频订单
     */
    @PutMapping
    @ApiOperation(value = "修改视频订单")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @OrderPermissions(orderId = "#orderVideoDTO.id")
    @OrderRefundVerify(videoId = "#orderVideoDTO.id")
    @Log(title = "修改视频订单", businessType = BusinessType.UPDATE)
    public R<String> updateOrderVideo(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) OrderVideoDTO orderVideoDTO) {
        orderService.updateOrderVideo(orderVideoDTO);
        return R.ok();
    }

    /**
     * 查看视频订单_视频详细信息（商家端）
     */
    @GetMapping(value = "/detail/company/{videoId}")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "查看视频订单_视频详细信息（商家端）", response = OrderVideoDetailVO.class)
    @OrderPermissions(orderId = "#videoId", businessShare = true)
    public R<OrderVideoDetailVO> getOrderVideoDetailCompanyInfo(@PathVariable("videoId") Long videoId) {
        return R.ok(orderService.getOrderVideoDetailCompanyInfo(videoId, true));
    }

    /**
     * 查看视频订单_视频详细信息（运营端）
     */
    @GetMapping(value = "/detail/back/{videoId}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查看视频订单_视频详细信息（运营端）", response = OrderVideoDetailVO.class)
    @RequiresPermissions(value = {"order:manage:details","task:afterSale:more","task:workOrder:more"}, logical = Logical.OR)
    public R<OrderVideoDetailVO> getOrderVideoDetailBackInfo(@PathVariable("videoId") Long videoId) {
        return R.ok(orderService.getOrderVideoDetailBackInfo(videoId, false));
    }

    @GetMapping(value = "/backend/member/detail/{orderNum}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查看会员订单_详细信息", response = OrderMemberDetailVO.class)
    @RequiresPermissions("order:vip:details")
    public R<OrderMemberDetailVO> getOrderMemberDetail(@PathVariable("orderNum") String orderNum) {
        return R.ok(orderMemberService.getOrderMemberDetail(orderNum));
    }

    /**
     * 查询视频订单匹配情况反馈
     */
    @GetMapping(value = "/case/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查询视频订单匹配情况反馈", response = OrderVideoCaseVO.class)
    @RequiresPermissions("order:manage:case")
    @OrderPermissions(orderId = "#id", include = {UserTypeConstants.USER_TYPE})
    public R<List<OrderVideoCaseVO>> selectOrderVideoCaseListByVideoId(@ApiParam("视频订单的主键") @PathVariable Long id) {
        return R.ok(orderService.selectOrderVideoCaseListByVideoId(id));
    }

    /**
     * 商家回复匹配情况反馈
     */
    @PostMapping(value = "/reply-video-case")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家回复匹配情况反馈")
    @OrderPermissions(orderId = "#replyVideoCaseDTO.videoId")
    @OrderRefundVerify(videoId = "#replyVideoCaseDTO.videoId")
    public R<String> replyVideoCase(@RequestBody @Validated ReplyVideoCaseDTO replyVideoCaseDTO) {
        orderService.replyVideoCase(replyVideoCaseDTO);
        return R.ok();
    }

    /**
     * 运营根据匹配情况修改订单
     */
    @PostMapping(value = "/operation-video-case")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营根据匹配情况修改订单")
    @OrderRefundVerify(videoId = "#dto.id")
    @RequiresPermissions("order:manage:case")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.THE_MATCHING_CONDITION_IS_MODIFIED, videoId = "#dto.id")
    @Log(title = "运营根据匹配情况修改订单", businessType = BusinessType.UPDATE)
    public R<String> operationVideoCase(@RequestBody @Validated OrderOperationVideoCaseDTO dto) {
        orderService.operationVideoCase(dto);
        return R.ok();
    }

    /**
     * 运营发起反馈
     */
    @PostMapping(value = "/send-video-case")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营发起反馈")
    @OrderRefundVerify(videoId = "#sendVideoCaseDTO.videoId")
    @RequiresPermissions("order:manage:case")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.MATCHING_FEEDBACK, videoId = "#sendVideoCaseDTO.videoId")
    @Log(title = "运营发起反馈", businessType = BusinessType.INSERT)
    public R<String> sendOrderVideoCase(@RequestBody @Validated SendVideoCaseDTO sendVideoCaseDTO) {
        orderService.sendOrderVideoCase(sendVideoCaseDTO);
        return R.ok();
    }

    /**
     * 取消订单
     */
    @PostMapping(value = "/cancel")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "取消订单")
    @RequiresPermissions("order:manage:cancel")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    public R<Void> cancelOrder(@ApiParam(value = "大订单号") @RequestParam String orderNum) {
        orderService.cancelOrder(orderNum, false);
        return R.ok();
    }

    @PostMapping(value = "/reopenOrder")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "开启订单")
    @Log(title = "开启订单", businessType = BusinessType.UPDATE)
    public R<String> reopenOrder(@ApiParam(value = "大订单号") @RequestParam String orderNum) {
        orderService.reopenOrder(orderNum);
        return R.ok();
    }

    /**
     * 取消会员订单
     */
    @PutMapping(value = "/cancelMemberOrder/{orderNum}")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isNoMock = false)
    @ApiOperation(value = "取消会员订单")
    @Log(title = "取消会员订单", businessType = BusinessType.UPDATE)
    public R<String> cancelMemberOrder(@PathVariable("orderNum") String orderNum) {
        orderService.companyApplyCancelMemberOrder(orderNum);
        return R.ok();
    }

    /**
     * 取消会员订单
     */
    @PutMapping(value = "/cancelMemberOrder")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isNoMock = false)
    @ApiOperation(value = "取消会员订单")
    @Log(title = "取消会员订单", businessType = BusinessType.UPDATE)
    public R<Void> cancelMemberOrders(@RequestParam List<String> orderNums) {
        orderService.cancelMemberOrders(orderNums);
        return R.ok();
    }

    /**
     * 取消会员订单
     */
    @PutMapping(value = "/backend/cancelMemberOrder/{orderNum}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "取消会员订单(运营端)")
    @RequiresPermissions("order:vip:cancel")
    @Log(title = "取消会员订单(运营端)", businessType = BusinessType.UPDATE)
    public R<String> cancelMemberOrderBackend(@PathVariable("orderNum") String orderNum) {
        orderService.cancelMemberOrder(orderNum);
        return R.ok();
    }

    /**
     * 取消会员订单（子账号入驻页）
     */
    @PutMapping(value = "/cancel-member-order-by-sub-account")
    @ApiOperation(value = "取消会员订单（子账号入驻页）")
    @Log(title = "取消会员订单（子账号入驻页）", businessType = BusinessType.UPDATE)
    public R<CheckWechatVO> cancelMemberOrderBySubAccount(@ApiParam("微信授权code") @RequestParam String code,
                                                          @ApiParam("订单号") @RequestParam List<String> orderNums,
                                                          @ApiParam("是否取消订单") @RequestParam Boolean isCancel) {
        CheckWechatVO checkWechatVO = orderService.cancelMemberOrderBySubAccount(code, orderNums, isCancel);
        return R.ok(checkWechatVO);
    }

    /**
     * 提交凭证信息
     */
    @PostMapping(value = "/submit-credential")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "提交凭证信息")
    @Log(title = "提交凭证信息", businessType = BusinessType.UPDATE)
    public R<String> submitCredential(@RequestBody @Validated SubmitCredentialDTO submitCredentialDTO) {
        orderService.submitCredential(submitCredentialDTO, false);
        return R.ok();
    }

    @PostMapping(value = "/payLock")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "下单锁定")
    @MemberAuth
    public R<String> payLock(@RequestBody @Valid OrderPayLockDTO dto) {
        orderService.payLock(dto);
        return R.ok();
    }
    @PostMapping(value = "/online-recharge/payLock")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "下单锁定(线上钱包充值)")
    public R<String> rechargePayLock(@RequestBody @Valid OrderPayLockDTO dto) {
        orderService.payLock(dto);
        return R.ok();
    }

    @PostMapping(value = "/member/payLock")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "会员下单锁定")
    public R<String> memberPayLock(@RequestBody @Valid OrderPayLockDTO dto) {
        orderService.payLock(dto);
        return R.ok();
    }

    /**
     * 余额支付
     */
    @PostMapping(value = "/balancePay")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "余额支付")
    public R<String> balancePay(@RequestBody @Valid BalancePayDTO dto) {
        orderService.balancePay(dto);
        return R.ok();
    }

    /**
     * 标记发货
     */
    @PostMapping(value = "/shipping-flag")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "标记发货")
    @OrderPermissions(orderId = "#flagShippingDTO.videoId", include = UserTypeConstants.USER_TYPE, businessShare = true)
    @RequiresPermissions(value = {"order:manage:shipments", "task:workOrder:reissue"}, logical = Logical.OR)
    @OrderRefundVerify(videoId = "#flagShippingDTO.videoId")
    public R<String> shippingFlag(@ApiParam("视频订单主键id") @RequestBody @Validated FlagShippingDTO flagShippingDTO) {
        orderService.shippingFlag(flagShippingDTO);
        return R.ok();
    }

    /**
     * 发货 or 补发
     */
    @PostMapping(value = "/shipping")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "发货 or 补发")
    @OrderPermissions(orderId = "#shippingDTO.videoId", include = UserTypeConstants.USER_TYPE, businessShare = true)
    @RequiresPermissions(value = {"order:manage:shipments", "task:workOrder:reissue"}, logical = Logical.OR)
    @OrderRefundVerify(videoId = "#shippingDTO.videoId")
    public R<String> shipping(@RequestBody @Validated ShippingDTO shippingDTO) {
        orderService.shipping(shippingDTO);
        return R.ok();
    }

    /**
     * 更换模特
     */
    @PostMapping(value = "/change-model")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "更换模特")
    @OrderPermissions(orderId = "#changeModelDTO.id")
    @OrderRefundVerify(videoId = "#changeModelDTO.id")
    public R<CreateOrderVO> changeModel(@RequestBody @Validated ChangeModelDTO changeModelDTO) {
        CreateOrderVO createOrderVO = orderService.changeModel(changeModelDTO.getId(), changeModelDTO.getModelId(), changeModelDTO.getReason());
        return R.ok(createOrderVO);
    }

    /**
     * 确认成品
     */
    @PostMapping(value = "/affirm")
    // @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "确认成品")
    // @OrderPermissions(orderId = "#dto.videoId")
    // @OrderRefundVerify(videoId = "#dto.videoId")
    // @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.MERCHANT_CONFIRMATION_MATERIAL, videoId = "#dto.videoId")
    public R<String> affirmGoods(@ApiParam("视频订单主键id") @RequestBody @Validated(OrderAffirmDTO.ValidationSequence.class) OrderAffirmDTO dto) {
        orderService.affirmGoods(dto);
        return R.ok();
    }

    /**
     * 运营帮商家上传素材
     */
    @PostMapping(value = "/back-help-upload-material")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营帮商家上传素材")
    @OrderRefundVerify(videoId = "#dto.videoId")
    @RequiresPermissions("order:manage:viewMaterial")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.BACK_CONFIRMATION_MATERIAL, videoId = "#dto.videoId")
    public R<String> backHelpUploadMaterial(@RequestBody @Validated(CommonValidatedGroup.SaveValidatedGroup.class) OrderVideoUploadLinkDTO dto) {
        orderService.backHelpUploadMaterial(dto);
        return R.ok();
    }

    /**
     * 查看素材
     */
    @GetMapping(value = "/get-upload-material/{videoId}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查看素材")
    @RequiresPermissions("order:manage:viewMaterial")
    @OrderPermissions(orderId = "#videoId", include = {UserTypeConstants.USER_TYPE})
    public R<OrderVideoUploadLinkVO> getUploadMaterial(@ApiParam("视频订单主键id") @PathVariable Long videoId) {
        OrderVideoUploadLinkVO result = orderVideoUploadLinkService.getUploadMaterial(videoId);
        return R.ok(result);
    }

    /**
     * 编辑素材
     */
    @PostMapping(value = "/edit-upload-material")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "编辑素材")
    @OrderRefundVerify(videoId = "#dto.videoId")
    public R<Void> editUploadMaterial(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) OrderVideoUploadLinkDTO dto) {
        orderVideoUploadLinkService.editUploadMaterial(dto);
        return R.ok();
    }

    /**
     * 商家端-订单各个状态统计
     */
    @GetMapping(value = "/merchant-status-count")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-订单各个状态统计")
    public R<OrderStatusVO> merchantStatusCount() {
        return R.ok(orderService.merchantOrderStatusCount());
    }

    /**
     * 商家端-订单各个状态统计
     */
    @GetMapping(value = "/workbenchOrderStatusCount")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家端-订单各个状态统计")
    public R<OrderStatusVO> workbenchOrderStatusCount() {
        return R.ok(orderService.workbenchOrderStatusCount());
    }

    /**
     * 运营端-订单各个状态统计
     */
    @GetMapping(value = "/back-status-count")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-订单各个状态统计")
    @RequiresPermissions("order:manage:list")
    public R<OrderStatusVO> backOrderStatusCount() {
        return R.ok(orderService.backOrderStatusCount());
    }

    /**
     * 加入购物车
     */
    @PostMapping(value = "/add-cart")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @Log(title = "加入购物车", businessType = BusinessType.INSERT)
    @ApiOperation(value = "加入购物车")
    @MemberAuth
    public R<CreateOrderVO> addCart(@RequestBody @Validated(OrderVideoDTO.AddVideoCartValidGroup.class) OrderVideoDTO orderVideoDTO) {
        CreateOrderVO createOrderVO = orderService.addCart(orderVideoDTO);
        return R.ok(createOrderVO);
    }

    /**
     * 购物车列表-下单运营下拉框
     */
    @GetMapping("/cart-create-user-select")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation("购物车列表-下单运营下拉框")
    public R<List<BusinessAccountSelectVO>> cartCreateUserSelect(@RequestParam(required = false) String keyword) {
        List<BusinessAccountSelectVO> select = videoCartService.cartCreateUserSelect(keyword);
        return R.ok(select);
    }

    /**
     * 购物车列表
     */
    @GetMapping(value = "/cart-list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "购物车列表")
    public R<PageInfo<VideoCartVO>> selectCartList(CartListDTO cartListDTO) {
        List<VideoCartVO> pageInfo = orderService.selectCartList(cartListDTO);
        return R.ok(toPage(pageInfo));
    }

    /**
     * 删除购物车订单
     */
    @DeleteMapping(value = "/delete-cart")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "删除购物车订单")
    public R<String> deleteCart(@RequestBody List<Long> cartIds) {
        orderService.deleteCart(cartIds);
        return R.ok();
    }

    /**
     * 查看购物车订单
     */
    @GetMapping(value = "/query-cart/{cartId}")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "查看购物车订单")
    public R<VideoCartVO> getCartInfo(@PathVariable Long cartId) {
        VideoCartVO videoCartVO = orderService.getCartInfo(cartId);
        return R.ok(videoCartVO);
    }

    /**
     * 编辑购物车
     */
    @PostMapping(value = "/edit-cart")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "编辑购物车")
    public R<CreateOrderVO> editCart(@RequestBody @Validated(OrderVideoDTO.EditVideoCartValidGroup.class) OrderVideoDTO orderVideoDTO) {
        return R.ok(orderService.editCart(orderVideoDTO));
    }

    /**
     * 购物车结算
     */
    @PostMapping(value = "/cart-settle-accounts")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "购物车结算", response = CreateOrderVO.class)
    @MemberAuth
    public R<CreateOrderVO> cartSettleAccounts(@RequestBody @Valid List<CartSettleAccountsDTO> cartSettleAccountsDTOS) {
        return R.ok(orderService.cartSettleAccounts(cartSettleAccountsDTOS));
    }

    /**
     * 复制购物车
     */
    @PostMapping(value = "/copy-cart")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "复制购物车")
    @MemberAuth
    public R<VideoCartVO> copyCart(@RequestParam Long cartId) {
        return R.ok(orderService.copyCart(cartId));
    }

    /**
     * 当前商户购物车数量统计
     */
    @GetMapping(value = "/cart-count")
    @ApiOperation(value = "当前商户购物车数量统计")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public R<Long> getCartCount() {
        Long count = orderService.getCartCount();
        return R.ok(count);
    }

    /**
     * 更改购物车意向模特
     */
    @PostMapping(value = "/update-cart-intention-model")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "更改购物车意向模特")
    public R<CreateOrderVO> updateCartIntentionModel(@RequestBody @Validated UpdateCartIntentionModelDTO dto) {
        return R.ok(orderService.updateCartIntentionModel(dto));
    }

    /**
     * 确认模特
     */
    @PostMapping(value = "/confirm-model")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "确认模特")
    @OrderPermissions(orderId = "#id")
    @OrderRefundVerify(videoId = "#id")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.CONFIRM_MODEL, videoId = "#id")
    public R<String> confirmModel(@RequestParam Long id) {
        orderService.confirmModel(id);
        return R.ok();
    }

    /**
     * 运营修改订单费用
     */
    @PostMapping(value = "/update-order-video-price")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改订单费用")
    @RequiresPermissions("order:manage:change-order-price")
    public R<String> updateOrderVideoPrice(@RequestBody @Validated UpdateOrderVideoPriceDTO dto) {
        orderService.updateOrderVideoPrice(dto);
        return R.ok();
    }

    /**
     * 审核订单_获取视频详细信息
     *
     * @param id 视频订单的主键
     */
    @GetMapping(value = "/operation/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "审核订单_获取视频详细信息", response = OrderOperationVideoVO.class)
    @RequiresPermissions("order:manage:edit")
    public R<OrderOperationVideoVO> getOperationOrderVideoInfo(@ApiParam("视频订单的主键") @PathVariable("id") Long id,
                                                               OrderListDTO dto) {
        OrderOperationVideoVO orderOperationVideoVO = orderService.getOperationOrderVideoInfo(id, dto);
        return R.ok(orderOperationVideoVO);
    }

    /**
     * 审核订单
     */
    @PutMapping("/operation")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "审核订单")
    @OrderPermissions(orderId = "#orderOperationVideoDTO.id")
    @OrderRefundVerify(videoId = "#orderOperationVideoDTO.id")
    @RequiresPermissions("order:manage:edit")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.REVIEW_ORDER, videoId = "#orderOperationVideoDTO.id")
    public R<CreateOrderVO> editOperationOrderVideoInfo(@RequestBody @Validated(OrderVideoDTO.ManagerTypeAuditOrAuditValidGroup.class) OrderOperationVideoDTO orderOperationVideoDTO) {
        CreateOrderVO createOrderVO = orderService.editOperationOrderVideoInfo(orderOperationVideoDTO);
        return R.ok(createOrderVO);
    }

    /**
     * 视频订单导出（运营端）
     */
    @Log(title = "视频订单导出（运营端）", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "视频订单导出（运营端）")
    @PostMapping("/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("order:manage:export")
    public void export(OrderListDTO orderListDTO, HttpServletResponse response) {
        if (CollUtil.isNotEmpty(orderListDTO.getPayType())){
            orderListDTO.setIsFilterClose(StatusTypeEnum.YES.getCode());
        }
        orderService.orderVideoExport(orderListDTO, response);
    }

    /**
     * 视频订单导出（商家端）
     */
    @Log(title = "视频订单导出（商家端）", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "视频订单导出（商家端）")
    @PostMapping("/company-export")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    public void companyExport(OrderListDTO orderListDTO, HttpServletResponse response) {
        orderService.orderVideoCompanyExport(orderListDTO, response);
    }

    /**
     * 上传产品图
     */
    @PostMapping("/upload-product-image")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("上传产品图")
    @OrderRefundVerify(videoId = "#videoId")
    @RequiresPermissions("order:manage:upload-productPic")
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.UPLOAD_PICTURES, videoId = "#videoId")
    public R<String> uploadProductImage(@RequestParam Long videoId,
                                        @RequestParam String productPic) {
        orderService.uploadProductImage(videoId, productPic);
        return R.ok();
    }

    /**
     * 订单统计
     */
    @PostMapping("/orderVideoStatistics")
    @ApiOperation(value = "订单统计")
    public R<OrderVideoStatisticsVO> orderVideoStatistics(@RequestBody OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
        return R.ok(orderService.orderVideoStatistics(orderVideoStatisticsDTO));
    }

    /**
     * 订单统计详情
     */
    @PostMapping("/orderVideoStatisticsDetail")
    @ApiOperation(value = "订单统计详情")
    public R<List<OrderVideoStatisticsDetailVO>> orderVideoStatisticsDetail(@RequestBody OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
        return R.ok(orderService.orderVideoStatisticsDetail(orderVideoStatisticsDTO));
    }

    /**
     * 根据条件查询视频订单列表
     */
    @GetMapping("/order-video-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("根据条件查询视频订单列表")
    public R<PageInfo<OrderVideo>> orderVideoList(OrderVideoListDTO orderVideoListDTO) {
        List<OrderVideo> orderVideoList = orderService.selectOrderVideoListByCondition(orderVideoListDTO);
        return R.ok(toPage(orderVideoList));
    }

    @GetMapping("/orderUserList")
    @ApiOperation("查询所有下单用户列表")
    public R<List<Long>> orderUserList() {
        List<Order> list = orderService.list();
        if (StringUtils.isEmpty(list)) {
            return R.ok(new ArrayList<Long>());
        }
        List<Long> orderUserIds = list.stream().filter(item -> StringUtils.isNotNull(item.getOrderUserId())).map(Order::getOrderUserId).collect(Collectors.toList());
        return R.ok(orderUserIds);
    }

    /**
     * 订单列表-获取拍摄模特下拉框（运营端）
     */
    @GetMapping("/order-shoot-model-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("订单列表-获取拍摄模特下拉框（运营端）")
    @RequiresPermissions("order:manage:list")
    public R<List<ModelSelectVO>> orderShootModelSelect(@RequestParam(required = false) String keyword) {
        List<ModelSelectVO> select = orderVideoService.orderShootModelSelect(keyword);
        return R.ok(select);
    }

    /**
     * 订单列表-获取对接人下拉框（运营端）
     */
    @GetMapping("/order-contact-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("订单列表-获取对接人下拉框（运营端）")
    @RequiresPermissions("order:manage:list")
    public R<List<UserVO>> orderContactSelect(@RequestParam(required = false) String keyword) {
        List<UserVO> select = orderVideoService.orderContactSelect(keyword);
        return R.ok(select);
    }

    /**
     * 订单列表-获取出单人下拉框（运营端）
     */
    @GetMapping("/order-issue-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("订单列表-获取出单人下拉框（运营端）")
    @RequiresPermissions("order:manage:list")
    public R<List<UserVO>> orderIssueSelect(@RequestParam(required = false) String keyword) {
        List<UserVO> select = orderVideoService.orderIssueSelect(keyword);
        return R.ok(select);
    }

    /**
     * 订单列表-获取订单运营下拉框（商家端）
     */
    @GetMapping("/company-order-user-select")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation("订单列表-获取订单运营下拉框（商家端）")
    public R<Set<String>> companyOrderUserSelect(@RequestParam(required = false) String keyword) {
        Set<String> select = orderVideoService.companyOrderUserSelect(keyword);
        return R.ok(select);
    }

    /**
     * 订单列表-获取订单运营下拉框（运营端）
     */
    @GetMapping("/back-create-order-user-name-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("订单列表-获取订单运营下拉框（运营端）")
    public R<List<BusinessAccountSelectVO>> backCreateOrderUserNameSelect(@RequestParam(required = false) String keyword) {
        List<BusinessAccountSelectVO> select = orderVideoService.backCreateOrderUserNameSelect(keyword);
        return R.ok(select);
    }

    /**
     * 订单列表-获取下单用户下拉框（运营端）
     */
    @GetMapping("/back-order-user-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("订单列表-获取下单用户下拉框（运营端）")
    @RequiresPermissions("order:manage:list")
    public R<List<BusinessAccountSelectVO>> backOrderUserSelect(@RequestParam(required = false) String keyword) {
        List<BusinessAccountSelectVO> select = orderVideoService.backOrderUserSelect(keyword);
        return R.ok(select);
    }

    /**
     * 获取订单总数
     */
    @GetMapping("/get-order-count")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation("获取订单总数")
    public R<Long> getOrderCount() {
        Long count = orderService.getOrderCount();
        return R.ok(count);
    }

    /**
     * 订单详情-变更记录
     */
    @GetMapping("/get-video-history-change-record/{videoId}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "订单详情-变更记录")
    @OrderPermissions(orderId = "#videoId", include = {UserTypeConstants.USER_TYPE})
    @RequiresPermissions("order:manage:change-record")
    public R<List<OrderVideoChangeLogVO>> getVideoHistoryChangeRecord(@PathVariable Long videoId) {
        List<OrderVideoChangeLogVO> list = orderService.getVideoHistoryChangeRecord(videoId);
        return R.ok(list);
    }

    /**
     * 重新加入购物车
     */
    @PostMapping("/rejoin-cart")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "重新加入购物车")
    @OrderPermissions(orderId = "#videoId")
    @MemberAuth
    public R<String> rejoinCart(@RequestParam Long videoId) {
        orderService.rejoinCart(videoId);
        return R.ok();
    }

    /**
     * 视频订单发货信息
     */
    @GetMapping("/shipping-info/{videoId}")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE, UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "视频订单发货信息")
    @OrderPermissions(orderId = "#videoId", include = UserTypeConstants.USER_TYPE, businessShare = true)
    @RequiresPermissions(value = {"order:manage:shipments", "task:workOrder:reissue"}, logical = Logical.OR)
    public R<ShippingVO> shippingInfo(@PathVariable Long videoId) {
        ShippingVO shippingInfo = orderService.shippingInfo(videoId);
        return R.ok(shippingInfo);
    }

    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @GetMapping("/getValidConfig")
    @ApiOperation(value = "根据账号类型获取收款人账号配置表")
    public R<PayeeAccountConfig> getValidConfig(Integer type) {
        return R.ok(payeeAccountConfigService.getValidConfigByAccountType(type));
    }

    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @PutMapping("/confirmReceipt/{logisticId}")
    @ApiOperation(value = "确认收货")
    public R<String> confirmReceipt(@PathVariable("logisticId") Long logisticId) {
        orderService.confirmReceipt(logisticId, null);
        return R.ok();
    }

    /**
     * 汇率异常调整
     */
    @PutMapping("/update-baidu-rate")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "汇率异常调整")
    public R<String> updateBaiduRate(@RequestBody @Validated UpdateBaiduRateDTO dto) {
        orderService.updateBaiduRate(dto);
        return R.ok();
    }

    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @GetMapping("/getBalanceLockRecord")
    @ApiOperation(value = "查看余额锁定记录")
    public R<List<BalanceLockRecordVO>> getBalanceLockRecord() {
        return R.ok(orderService.getBalanceLockRecord());
    }

    @DeleteMapping("/member/latestUnPayOrder")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isNoMock = false)
    @ApiOperation(value = "关闭过往订单")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    public R<String> cancelLatestUnPayOrderNumber() {
        orderService.cancelLatestUnPayOrderNumber();
        return R.ok("");
    }

    /**
     * 查询下单运营列表
     */
    @GetMapping("/user/nickname/list")
    @LoginUserType(userTypes = {UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "查询下单运营列表", response = PageInfo.class)
    public R<List<String>> selectOrderUserNicknameList(@RequestParam(required = false) String name) {
        return R.ok(orderService.selectOrderUserNicknameList(name));
    }


    /**
     * 获取待完成、需确认状态下的订单的出单人信息
     */
    @GetMapping("/un-finished-and-need-confirm-order-issue-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取待完成、需确认状态下的订单的出单人信息")
    @RequiresPermissions("order:manage:create-work-order")
    public R<List<UnFinishedAndNeedConfirmOrderIssueSelectVO>> unFinishedAndNeedConfirmOrderIssueSelect() {
        return R.ok(orderService.unFinishedAndNeedConfirmOrderIssueSelect());
    }
    /**
     * 运营手动获取产品图
     */
    @PostMapping("/crawl-product-pic")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营手动获取产品图")
    public R<String> crawlProductPic(@RequestParam Long videoId) {
        orderService.crawlProductPic(videoId);
        return R.ok();
    }

    /**
     * 订单回退
     */
    @PostMapping("/rollback-order")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "订单回退")
    @RequiresPermissions("order:manage:rollback")
    public R<String> rollbackOrder(@RequestBody RollbackOrderDTO dto) {
        orderService.rollbackOrder(dto);
        return R.ok();
    }

    /**
     * 根据originNumber获取退单信息
     */
    @PostMapping("/getOrderVideoRefundList")
    public R<List<OrderVideoRefund>> getOrderVideoRefundList(@RequestBody List<String> numbers) {
        return R.ok(orderService.getOrderVideoRefundList(numbers));
    }

    /**
     * 获取会员有效订单
     */
    @GetMapping("/get-valid-order-list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取会员有效订单")
    public R<List<OrderMember>> getValidOrderMemberList(@RequestParam Long bizUserId) {
        return R.ok(orderMemberService.getValidOrderMemberList(bizUserId));
    }

    /**
     * 视频订单列表-获取意向模特下拉框
     */
    @GetMapping("/order-intention-model-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "视频订单列表-获取意向模特下拉框", response = ModelInfoVO.class)
    public R<List<ModelSelectVO>> orderIntentionModelSelect(@RequestParam(required = false) String keyword) {
        List<ModelSelectVO> modelInfoVOS = orderVideoService.orderIntentionModelSelect(keyword);
        return R.ok(modelInfoVOS);
    }
}
