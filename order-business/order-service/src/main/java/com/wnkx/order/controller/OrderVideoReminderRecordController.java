package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderCountVO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListVO;
import com.wnkx.order.annotations.OrderPermissions;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.service.OrderVideoReminderRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:16
 */
@RestController
@RequestMapping("/reminder")
@Api(value = "订单催单服务", tags = "订单催单服务")
@RequiredArgsConstructor
@Validated
public class OrderVideoReminderRecordController extends BaseController {

    private final OrderVideoReminderRecordService orderVideoReminderRecordService;

    /**
     * 商家催一催
     */
    @PostMapping
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation("商家催一催")
    @OrderPermissions(orderId = "#videoId")
    @OrderRefundVerify(videoId = "#videoId")
    public R<Boolean> reminder(@RequestParam Long videoId) {
        Boolean result = orderVideoReminderRecordService.reminder(videoId);
        return R.ok(result);
    }

    /**
     * 催单列表
     */
    @GetMapping("/list-v2")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("催单列表")
    @RequiresPermissions("task:reminder:list")
    public R<PageInfo<OrderVideoReminderRecordListVO>> selectListByCondition(OrderVideoReminderRecordListDTO dto) {
        List<OrderVideoReminderRecordListVO> list = orderVideoReminderRecordService.selectListByCondition(dto);
        return R.ok(toPage(list));
    }

    /**
     * 总催单次数统计
     */
    @GetMapping("/reminder-count")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("总催单次数统计")
    @RequiresPermissions("task:reminder:list")
    public R<OrderVideoReminderCountVO> reminderCount() {
        OrderVideoReminderCountVO result = orderVideoReminderRecordService.reminderCount();
        return R.ok(result);
    }

    /**
     * 运营已催单
     */
    @PutMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("运营已催单")
    @OrderRefundVerify(videoId = "#videoId")
    @RequiresPermissions("order:manage:reminder")
    public R<String> urged(@RequestParam Long videoId) {
        orderVideoReminderRecordService.urged(videoId);
        return R.ok();
    }
}
