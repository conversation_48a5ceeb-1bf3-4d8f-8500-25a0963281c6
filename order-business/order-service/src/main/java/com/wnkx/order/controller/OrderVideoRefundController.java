package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.BackUserTypeEnum;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRefundVO;
import com.ruoyi.system.api.domain.vo.order.RefundInfoVO;
import com.wnkx.order.annotations.OrderPermissions;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.service.IOrderVideoRefundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订单_视频_退款Controller
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@RestController
@RequestMapping("/refund")
@Api(value = "订单_视频_退款服务", tags = "订单_视频_退款服务")
@RequiredArgsConstructor
public class OrderVideoRefundController extends BaseController {
    private final IOrderVideoRefundService orderVideoRefundService;

    /**
     * 退款订单列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE,UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "退款订单列表", response = PageInfo.class)
    @RequiresPermissions("finance:refundApproval:list")
    public R<PageInfo<OrderVideoRefundVO>> list(OrderVideoRefundListDTO orderVideoRefundListDTO) {
        List<OrderVideoRefundVO> list = orderVideoRefundService.selectOrderVideoRefundListByCondition(orderVideoRefundListDTO);
        return R.ok(toPage(list));
    }

    /**
     * 导出退款订单
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出退款订单")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("finance:refundApproval:export")
    public void export(HttpServletResponse response, OrderVideoRefundListDTO orderVideoRefundListDTO) {
        List<OrderVideoRefundExportDTO> orderVideoRefundExportDTOS = orderVideoRefundService.exportExcel(orderVideoRefundListDTO);
        ExcelUtil<OrderVideoRefundExportDTO> util = new ExcelUtil<>(OrderVideoRefundExportDTO.class);

        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "退款订单导出");
        util.exportExcel(response, orderVideoRefundExportDTOS, "退款订单导出");
    }

    /**
     * 申请退款-获取退款信息
     */
    @PostMapping("get-refund-info")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE,UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "申请退款-获取退款信息")
    @RequiresPermissions(value = {"order:manage:refund","task:workOrder:compensate"}, logical = Logical.OR)
    @OrderPermissions(orderId = "#getRefundInfoDTO.videoId", include = {UserTypeConstants.USER_TYPE})
    public R<RefundInfoVO> getRefundInfo(@RequestBody @Validated RefundInfoDTO getRefundInfoDTO) {
        RefundInfoVO info = orderVideoRefundService.getRefundInfoVo(getRefundInfoDTO);
        return R.ok(info);
    }

    /**
     * 申请退款
     */
    @PostMapping("apply-refund")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE,UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "申请退款")
    @OrderPermissions(orderId = "#applyRefundDTO.videoId", backUserType = BackUserTypeEnum.CHINESE, workOrderAssignee = true)
    @OrderRefundVerify(videoId = "#applyRefundDTO.videoId")
    @RequiresPermissions(value = {"order:manage:refund","task:workOrder:compensate"}, logical = Logical.OR)
    public R<String> applyRefund(@RequestBody @Validated ApplyRefundDTO applyRefundDTO) {
        orderVideoRefundService.applyRefund(applyRefundDTO);
        return R.ok();
    }

    /**
     * 同意退款
     */
    @PostMapping("agree-refund")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "同意退款")
    @RequiresPermissions("finance:refundApproval:confirm")
    public R<String> agreeRefund(@ApiParam("退款订单主键") @RequestBody @Validated AgreeRefundDTO dto) {
        orderVideoRefundService.agreeRefund(dto);
        return R.ok();
    }

    /**
     * 拒绝退款
     */
    @PostMapping("reject-refund")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "拒绝退款")
    @RequiresPermissions("finance:refundApproval:confirm")
    public R<String> rejectRefund(@ApiParam("退款订单主键") @RequestBody @Validated RejectRefundDTO rejectRefundDTO) {
        orderVideoRefundService.rejectRefund(rejectRefundDTO);
        return R.ok();
    }

    /**
     * 商家取消退款
     */
    @PostMapping("cancel-refund")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "商家取消退款")
    public R<String> cancelRefund(@ApiParam("退款订单主键") @RequestBody List<Long> id) {
        orderVideoRefundService.cancelRefund(id);
        return R.ok();
    }
}
