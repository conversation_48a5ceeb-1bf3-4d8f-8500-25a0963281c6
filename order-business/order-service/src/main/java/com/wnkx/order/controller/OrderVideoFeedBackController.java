package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.MaterialRejectDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFeedBackDTO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackMaterialVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackVO;
import com.wnkx.order.annotations.OrderPermissions;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.service.OrderVideoFeedBackMaterialService;
import com.wnkx.order.service.OrderVideoFeedBackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 订单反馈
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@RestController
@RequestMapping("/feedback")
@Api(value = "订单反馈服务", tags = "订单服务-订单反馈服务")
@RequiredArgsConstructor
@Validated
public class OrderVideoFeedBackController {

    private final OrderVideoFeedBackService orderVideoFeedBackService;
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;

    @PostMapping("/model/reject")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "驳回反馈素材")
    @RequiresPermissions("order:manage:feedbackMaterial-reject")
    public R<String> rejectModelMaterial(@RequestBody @Validated MaterialRejectDTO materialRejectDTO) {
        orderVideoFeedBackMaterialService.rejectModelMaterial(materialRejectDTO);
        return R.ok();
    }


    /**
     * 模特反馈素材列表
     *
     * @param videoId 视频订单id
     */
    @GetMapping("/model/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "模特反馈素材列表")
    @RequiresPermissions("order:manage:modelFeedbackMaterial")
    public R<List<OrderFeedBackMaterialVO>> modelFeedBackList(@RequestParam Long videoId) {
        List<OrderFeedBackMaterialVO> list = orderVideoFeedBackMaterialService.modelFeedBackList(videoId);
        return R.ok(list);
    }
    /**
     * 标记下载状态
     */
    @PostMapping("/model/{id}/markDownload")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "标记下载状态")
    @RequiresPermissions("order:manage:modelFeedbackMaterial")
    @Log(title = "标记下载状态",businessType = BusinessType.UPDATE)
    public R<String> download(@PathVariable Long id) {
        orderVideoFeedBackMaterialService.markDownload(id);
        return R.ok();
    }

    /**
     * 添加反馈素材给商家
     */
    @PostMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "添加反馈素材给商家")
    @RequiresPermissions(value = {"order:manage:feedbackMaterial", "task:workOrder:feedbackMaterial","task:afterSale:feedbackMaterial"},logical = Logical.OR)
    @OrderRefundVerify(videoId = "#dto.videoId")
    @Log(title = "添加反馈素材给商家",businessType = BusinessType.INSERT)
    public R<String> addFeedBack(@RequestBody @Validated OrderVideoFeedBackDTO dto) {
        orderVideoFeedBackService.addFeedBack(dto);
        return R.ok();
    }

    /**
     * 根据订单id查询商家反馈素材（商家端）
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "根据订单id查询商家反馈素材", response = PageInfo.class)
    @OrderPermissions(orderId = "#videoId")
    public R<List<OrderFeedBackVO>> list(@RequestParam Long videoId) {
        final List<OrderFeedBackVO> feedBackList = orderVideoFeedBackService.getFeedBackList(Collections.singletonList(videoId));
        return R.ok(feedBackList);
    }


    /**
     * 根据订单id查询商家反馈素材（运营端）
     */
    @GetMapping("/lists")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "根据订单id查询商家反馈素材", response = PageInfo.class)
    @RequiresPermissions(value = "order:manage:feedbackMaterial")
    public R<List<OrderFeedBackVO>> lists(@RequestParam Long videoId) {
        final List<OrderFeedBackVO> feedBackList = orderVideoFeedBackService.getFeedBack(Collections.singletonList(videoId));
        return R.ok(feedBackList);
    }
    /**
     * 根据订单id查询商家反馈素材（运营端）
     */
    @GetMapping("/roastOrderFeedBackList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "根据订单id查询商家反馈素材(吐槽获取)", response = PageInfo.class)
    public R<List<OrderFeedBackVO>> roastOrderFeedBackList(@RequestParam Long videoId) {
        final List<OrderFeedBackVO> feedBackList = orderVideoFeedBackService.getFeedBackList(Collections.singletonList(videoId));
        return R.ok(feedBackList);
    }
}
