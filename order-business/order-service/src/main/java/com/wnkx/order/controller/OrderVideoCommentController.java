package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.order.OrderCommentDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoComment;
import com.wnkx.order.service.OrderVideoCommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单备注
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@RestController
@RequestMapping("/comment")
@Api(value = "订单备注服务", tags = "订单服务-订单备注服务")
@RequiredArgsConstructor
@Validated
public class OrderVideoCommentController {

    private final OrderVideoCommentService orderVideoCommentService;


    /**
     * 添加订单备注
     */
    @PostMapping("")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "添加订单备注")
    public R<String> addOrderComment(@RequestBody @Validated OrderCommentDTO orderCommentDTO) {
        orderVideoCommentService.addOrderComment(orderCommentDTO);
        return R.ok();
    }

    /**
     * 获取订单备注列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取订单备注列表", response = PageInfo.class)
    public R<List<OrderVideoComment>> list(@RequestParam Long videoId) {
        final List<OrderVideoComment> orderVideoComments = orderVideoCommentService.orderCommentList(videoId);
        return R.ok(orderVideoComments);
    }
}
