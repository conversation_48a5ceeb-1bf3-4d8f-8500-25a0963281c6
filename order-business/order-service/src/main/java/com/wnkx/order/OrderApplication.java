package com.wnkx.order;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import com.wnkx.common.lb.annotation.EnableFeignInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@EnableDiscoveryClient
@EnableTransactionManagement
@EnableCaching
@SpringBootApplication(scanBasePackages = {"com.wnkx.order", "com.ruoyi.common.security.aspect"})
@MapperScan(basePackages = {"com.wnkx.order.**.mapper"})
@EnableRyFeignClients
@EnableCustomSwagger2
@EnableFeignInterceptor
@RestController
@EnableCustomConfig
public class OrderApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderApplication.class, args);
    }

    @GetMapping("/hello")
    public String hello() {
        return "hello";
    }

}

