package com.wnkx.order.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.logistic.*;
import com.ruoyi.system.api.domain.vo.order.logistic.LogisticFollowModelListVO;
import com.ruoyi.system.api.domain.vo.order.logistic.MemberCodeListVO;
import com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowVO;
import com.wnkx.order.service.IOrderVideoLogisticService;
import com.wnkx.order.service.OrderVideoLogisticFollowService;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2025-04-24 10:00:03
 */
@RestController
@Api(value = "物流跟进服务", tags = "物流跟进服务")
@RequestMapping("/logistic/follow")
@RequiredArgsConstructor
@Validated
public class OrderVideoLogisticFollowController extends BaseController {

    private final OrderVideoLogisticFollowService orderVideoLogisticFollowService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;
    private final IOrderVideoLogisticService orderVideoLogisticService;

    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询物流跟进服务列表", response = PageInfo.class)
    @RequiresPermissions(value = {"task:logistics:notDelivered", "task:logistics:delivered"}, logical = Logical.OR)
    public R<PageInfo<OrderVideoLogisticFollowVO>> selectOrderListByCondition(@Validated OrderVideoLogisticFollowListDTO dto) {
        List<OrderVideoLogisticFollowVO> list = orderVideoLogisticFollowService.selectOrderVideoLogisticFollowList(dto);
        return R.ok(toPage(list));
    }

    @GetMapping("/detail/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询物流跟进服务详情")
    @RequiresPermissions(value = {"task:logistics_0:detail", "task:logistics_1:detail"}, logical = Logical.OR)
    public R<OrderVideoLogisticFollowVO> getDetailById(@PathVariable("id") Long id) {
        return R.ok(orderVideoLogisticFollowService.getDetailById(id));
    }
    @GetMapping("/getStatisticsVO")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取物流统计数据")
    public R<OrderVideoLogisticFollowStatisticsVO> getStatisticsVO() {
        return R.ok(orderVideoLogisticFollowService.getStatisticsVO());
    }

    @PostMapping(value = "/alertShipping")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "标记通知：通知发货、提醒发货、通知地址变更")
    @RequiresPermissions(value = "task:logistics:mark")
    @Log(title = "标记通知", businessType = BusinessType.UPDATE)
    public R<String> alertShipping(@RequestBody @Validated AlertShippingDTO dto) {
        orderVideoLogisticCore.alertShipping(dto);
        return R.ok();
    }

    @PostMapping(value = "/delayShipping")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "延迟发货")
    @Log(title = "延迟发货", businessType = BusinessType.UPDATE)
    public R<String> delayShipping(@RequestBody @Validated DelayShippingDTO dto) {
        orderVideoLogisticCore.delayShipping(dto);
        return R.ok();
    }


    @PostMapping(value = "/remarkReplenish")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "补充说明")
    @Log(title = "补充说明", businessType = BusinessType.UPDATE)
    public R<String> remarkReplenish(@RequestBody @Validated AlertShippingDTO dto) {
        orderVideoLogisticCore.remarkReplenish(dto);
        return R.ok();
    }

    @PostMapping(value = "/logisticFollow")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "物流跟进")
    @RequiresPermissions(value = "task:logistics:followUp")
    @Log(title = "物流跟进", businessType = BusinessType.UPDATE)
    public R<String> logisticFollow(@RequestBody @Validated LogisticFollowDTO dto) {
        dto.setIsCallBack(StatusTypeEnum.NO.getCode());
        orderVideoLogisticCore.logisticFollow(dto);
        return R.ok();
    }

    @PostMapping(value = "/modelConfirm")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "模特确认")
    @RequiresPermissions(value = "task:logistics:modelConfirm")
    @Log(title = "模特确认", businessType = BusinessType.UPDATE)
    public R<String> modelConfirm(@RequestBody @Validated ModelConfirmDTO dto) {
        orderVideoLogisticCore.modelConfirm(dto);
        return R.ok();
    }

    @PostMapping(value = "/operationVideoLogistic")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "操作物流单")
    @Log(title = "操作物流单", businessType = BusinessType.UPDATE)
    public R<String> operationVideoLogistic(@RequestBody @Validated OperationVideoLogisticDTO dto) {
        orderVideoLogisticService.operationVideoLogistic(dto);
        return R.ok();
    }

    @GetMapping(value = "/logistic/checkRepetition/{number}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查看物流单号是否重复")
    public R<Boolean> checkLogisticNumberRepetition(@PathVariable("number") String number) {
        return R.ok(CollUtil.isNotEmpty(orderVideoLogisticService.selectListByNumber(number)));
    }

    /**
     * 物流跟进-获取对接人下拉框（运营端）
     */
    @GetMapping("/contact-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("物流跟进-获取对接人下拉框（运营端）")
    public R<List<UserVO>> orderContactSelect(@RequestParam(required = false) String keyword, @RequestParam(required = false) Integer followStatus) {
        List<UserVO> select = orderVideoLogisticFollowService.orderContactSelect(keyword, followStatus);
        return R.ok(select);
    }

    /**
     * 物流跟进-获取出单人下拉框（运营端）
     */
    @GetMapping("/issue-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("物流跟进-获取出单人下拉框（运营端）")
    public R<List<UserVO>> orderIssueSelect(@RequestParam(required = false) String keyword, @RequestParam(required = false) Integer followStatus) {
        List<UserVO> select = orderVideoLogisticFollowService.orderIssueSelect(keyword,followStatus);
        return R.ok(select);
    }
    /**
     * 物流跟进-获取商家编码及数量（运营端）
     */
    @GetMapping("/memberCodeList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("物流跟进-获取商家编码及数量（运营端）")
    public R<List<MemberCodeListVO>> memberCodeList(OrderVideoLogisticFollowListDTO dto) {
        return R.ok(orderVideoLogisticFollowService.memberCodeList(dto));
    }
    /**
     * 物流跟进-获取商家编码及数量（运营端）
     */
    @GetMapping("/modelListSelectmodelListSelect")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("物流跟进-获取模特及数量（运营端）")
    public R<List<LogisticFollowModelListVO>> modelListSelect(OrderVideoLogisticFollowListDTO dto) {
        return R.ok(orderVideoLogisticFollowService.modelListSelect(dto));
    }

    @PostMapping("/test/logisticFollowNotify")
    @ApiOperation("测试物流推送")
    @RequiresPermissions(value = "test:logisticFollowNotify")
    public R<String> logisticFollowNotify(@RequestBody @Validated LogisticFollowNotifyTestDTO dto) {
        orderVideoLogisticCore.logisticFollowNotify(BeanUtil.copyProperties(dto, LogisticFollowNotifyDTO.class));
        return R.ok("通知成功");
    }


}
