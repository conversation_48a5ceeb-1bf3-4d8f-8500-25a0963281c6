package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.annotations.OrderRefundVerify;
import com.wnkx.order.annotations.OrderVideoOperate;
import com.wnkx.order.service.IOrderVideoFeedBackMaterialInfoService;
import com.wnkx.order.service.OrderVideoFeedBackMaterialService;
import com.wnkx.order.service.OrderVideoFeedBackService;
import com.wnkx.order.service.OrderVideoUploadLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/3/20 15:34
 */
@RestController
@RequestMapping("/edit")
@Api(value = "剪辑管理服务", tags = "剪辑管理服务")
@RequiredArgsConstructor
@Validated
public class OrderVideoEditController extends BaseController {

    private final IOrderVideoFeedBackMaterialInfoService orderVideoFeedBackMaterialInfoService;
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;
    private final OrderVideoUploadLinkService orderVideoUploadLinkService;
    private final OrderVideoFeedBackService orderVideoFeedBackService;

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 列表
     */
    @GetMapping("/material-info-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-待下载、待剪辑、待反馈、需确认 列表")
    public R<PageInfo<MaterialInfoListVO>> selectMaterialInfoListByCondition(MaterialInfoListDTO dto) {
        List<MaterialInfoListVO> materialInfoList = orderVideoFeedBackMaterialInfoService.selectMaterialInfoListByCondition(dto);
        return R.ok(toPage(materialInfoList));
    }

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 拍摄模特下拉框
     */
    @GetMapping("/select-shoot-model")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-待下载、待剪辑、待反馈、需确认 拍摄模特下拉框")
    public R<List<ModelSelectVO>> selectShootModel(@RequestParam @ApiParam("模特反馈素材详情状态") Integer status) {
        List<ModelSelectVO> materialInfoList = orderVideoFeedBackMaterialInfoService.selectShootModel(status);
        return R.ok(materialInfoList);
    }

    /**
     * 剪辑管理-待下载 领取人下拉框
     */
    @GetMapping("/select-receive-person")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-待下载 领取人下拉框")
    public R<List<UserVO>> selectReceivePerson() {
        List<UserVO> userVOS = orderVideoFeedBackMaterialInfoService.selectReceivePerson();
        return R.ok(userVOS);
    }

    /**
     * 剪辑管理-待反馈、需确认 剪辑人下拉框
     */
    @GetMapping("/select-edit-person")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-待反馈、需确认 剪辑人下拉框")
    public R<List<UserVO>> selectEditPerson(@RequestParam @ApiParam("模特反馈素材详情状态") Integer status) {
        List<UserVO> userVOS = orderVideoFeedBackMaterialInfoService.selectEditPerson(status);
        return R.ok(userVOS);
    }

    /**
     * 剪辑管理-待下载列表统计
     */
    @GetMapping("/get-material-info-statistics")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-待下载列表统计")
    public R<GetMaterialInfoStatisticsVO> getMaterialInfoStatistics() {
        GetMaterialInfoStatisticsVO getMaterialInfoStatisticsVO = orderVideoFeedBackMaterialInfoService.getMaterialInfoStatistics();
        return R.ok(getMaterialInfoStatisticsVO);
    }

    /**
     * 剪辑管理-历史剪辑记录
     */
    @GetMapping("/history-edit-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-历史剪辑记录")
    public R<List<SelectHistoryEditListVO>> selectHistoryEditList(@RequestParam Long videoId) {
        List<SelectHistoryEditListVO> materialInfoList = orderVideoFeedBackMaterialInfoService.selectHistoryEditList(videoId);
        return R.ok(materialInfoList);
    }

    /**
     * 剪辑管理-领取
     */
    @PostMapping("/get-edit")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-领取")
    @RequiresPermissions(value = {"my:clip:get", "my:clip:getMore"}, logical = Logical.OR)
    public R<Void> getEdit(@RequestBody @Validated GetEditDTO dto) {
        orderVideoFeedBackMaterialInfoService.getEdit(dto);
        return R.ok();
    }

    /**
     * 剪辑管理-标记下载-校验是否存在进行中的任务
     */
    @GetMapping("/check-download")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-标记下载-校验是否存在进行中的任务")
    @RequiresPermissions(value = "my:clip:tagDownload")
    public R<Boolean> checkDownload(@RequestParam @ApiParam("视频订单ID") Long videoId) {
        Boolean result = orderVideoFeedBackMaterialInfoService.checkDownload(videoId);
        return R.ok(result);
    }

    /**
     * 剪辑管理-标记下载
     */
    @PostMapping("/mark-download")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-标记下载")
    @RequiresPermissions(value = "my:clip:tagDownload")
    public R<Void> markDownload(@RequestParam @ApiParam("模特反馈素材详情ID") Long id) {
        orderVideoFeedBackMaterialInfoService.markDownload(id);
        return R.ok();
    }

    /**
     * 剪辑管理-历史剪辑要求
     */
    @GetMapping("/history-clip-record")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-历史剪辑要求")
    public R<HistoryClipRecordVO> getHistoryClipRecord(@RequestParam @ApiParam("视频订单ID") Long videoId) {
        HistoryClipRecordVO historyClipRecord = orderVideoFeedBackMaterialInfoService.getHistoryClipRecord(videoId);
        return R.ok(historyClipRecord);
    }

    /**
     * 剪辑管理-标记已剪辑
     */
    @PostMapping("/mark-clip")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-标记已剪辑")
    @RequiresPermissions(value = "my:clip:signEdit")
    public R<Void> markClip(@RequestBody @Validated MarkClipDTO dto) {
        orderVideoFeedBackMaterialInfoService.markClip(dto);
        return R.ok();
    }

    /**
     * 剪辑管理-不反馈给商家
     */
    @PostMapping("/mark-no-feedback")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-不反馈给商家")
    @RequiresPermissions(value = "my:clip:noFeedback")
    public R<Void> markNoFeedback(@RequestBody @Validated MarkNoFeedbackDTO dto) {
        orderVideoFeedBackMaterialInfoService.markNoFeedback(dto);
        return R.ok();
    }

    /**
     * 校验能否评分
     */
    @GetMapping("/check-can-score")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "校验能否评分")
    public R<Boolean> checkCanScore(@RequestParam Long videoId) {
        Boolean canScore = orderVideoFeedBackMaterialInfoService.checkCanScore(videoId);
        return R.ok(canScore);
    }

    /**
     * 剪辑管理-添加反馈素材给商家
     */
    @PostMapping("/add-feed-back")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-添加反馈素材给商家")
    @RequiresPermissions(value = {"order:manage:feedbackMaterial", "task:workOrder:feedbackMaterial", "task:afterSale:feedbackMaterial", "my:clip:feedback"}, logical = Logical.OR)
    @OrderRefundVerify(videoId = "#dto.videoId")
    public R<Void> addFeedBack(@RequestBody @Validated(OrderVideoFeedBackDTO.newAddFeedBackValidGroup.class) OrderVideoFeedBackDTO dto) {
        orderVideoFeedBackMaterialInfoService.addFeedBack(dto);
        return R.ok();
    }

    /**
     * 剪辑管理-需确认列表-导出
     */
    @PostMapping("/export-need-confirm-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-需确认列表-导出")
    public void exportNeedConfirmList(MaterialInfoListDTO dto, HttpServletResponse response){
        orderVideoFeedBackMaterialInfoService.exportNeedConfirmList(dto, response);
    }

    /**
     * 剪辑管理-待上传、已完成 列表
     */
    @GetMapping("/upload-link-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-待上传、已完成 列表")
    public R<PageInfo<UploadLinkListVO>> selectUploadLinkListByCondition(UploadLinkListDTO dto) {
        List<UploadLinkListVO> uploadLinkListVOS = orderVideoUploadLinkService.selectUploadLinkListByCondition(dto);
        return R.ok(toPage(uploadLinkListVOS));
    }

    /**
     * 剪辑管理-待上传、已完成 拍摄模特下拉框
     */
    @GetMapping("/upload-link-list-select-shoot-model")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-待上传、已完成 拍摄模特下拉框")
    public R<List<ModelSelectVO>> uploadLinkListSelectShootModel(@RequestParam @ApiParam("上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传）") List<Integer> status) {
        List<ModelSelectVO> modelInfoVOS = orderVideoUploadLinkService.uploadLinkListSelectShootModel(status);
        return R.ok(modelInfoVOS);
    }

    /**
     * 剪辑管理-历史上传记录
     */
    @GetMapping("/history-upload-record")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-历史上传记录")
    public R<List<HistoryUploadRecordVO>> getHistoryUploadRecord(@RequestParam @ApiParam("上传素材ID") Long uploadLinkId) {
        List<HistoryUploadRecordVO> historyUploadRecordVOS = orderVideoUploadLinkService.getHistoryUploadRecord(uploadLinkId);
        return R.ok(historyUploadRecordVOS);
    }

    /**
     * 剪辑管理-标记上传账号
     */
    @PostMapping("/mark-upload-account")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-标记上传账号")
    @RequiresPermissions(value = "my:clip:selectAccount")
    public R<Void> markUploadAccount(@RequestBody @Validated MarkUploadAccountDTO dto) {
        orderVideoUploadLinkService.markUploadAccount(dto);
        return R.ok();
    }

    /**
     * 剪辑管理-标记上传账号-下拉框
     */
    @GetMapping("/mark-upload-account-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-标记上传账号-下拉框")
    @RequiresPermissions(value = "my:clip:selectAccount")
    public R<List<MarkUploadAccountSelectVO>> markUploadAccountSelect(@ApiParam("上传链接ID（待上传、已完成的列表ID）") @RequestParam(required = false) Long uploadLinkId) {
        List<MarkUploadAccountSelectVO> uploadAccountSelect = orderVideoUploadLinkService.getMarkUploadAccountSelect(uploadLinkId);
        return R.ok(uploadAccountSelect);
    }

    /**
     * 剪辑管理-上传账号-下拉框
     */
    @GetMapping("/upload-account-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-上传账号-下拉框")
    public R<Set<String>> uploadAccountSelect() {
        Set<String> uploadAccountSelect = orderVideoUploadLinkService.uploadAccountSelect();
        return R.ok(uploadAccountSelect);
    }

    /**
     * 剪辑管理-标记为待确认上传
     */
    @PostMapping("/mark-upload-confirm")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-标记为待确认上传")
    @RequiresPermissions(value = "my:clip:sign")
    public R<Void> markUploadConfirm(@RequestParam Long uploadLinkId) {
        orderVideoUploadLinkService.markUploadConfirm(uploadLinkId);
        return R.ok();
    }

    /**
     * 剪辑管理-上传成功
     */
    @PostMapping(value = "/upload-link")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-上传成功")
    @RequiresPermissions(value = "my:clip:uploadSuccess")
    public R<Void> uploadLink(@RequestBody @Validated(CommonValidatedGroup.SaveValidatedGroup.class) OrderUploadLinkDTO dto) {
        orderVideoUploadLinkService.uploadLink(dto);
        return R.ok();
    }

    /**
     * 剪辑管理-上传失败
     */
    @PostMapping(value = "/upload-link-fail")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-上传失败")
    @RequiresPermissions(value = "my:clip:uploadError")
    public R<Void> uploadLinkFail(@RequestBody @Validated OrderUploadLinkDTO dto) {
        orderVideoUploadLinkService.uploadLinkFail(dto);
        return R.ok();
    }

    /**
     * 剪辑管理-取消上传
     */
    @PostMapping(value = "/cancel-upload-link")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-取消上传")
    @RequiresPermissions(value = "my:clip:uploadCancel")
    public R<Void> cancelUploadLink(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) OrderUploadLinkDTO dto) {
        orderVideoUploadLinkService.cancelUploadLink(dto);
        return R.ok();
    }

    /**
     * 剪辑管理-已关闭 列表
     */
    @GetMapping("/closed-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "剪辑管理-已关闭 列表")
    public R<PageInfo<ClosedListVO>> selectClosedListByCondition(ClosedListDTO dto) {
        List<ClosedListVO> closedListVOS = orderVideoFeedBackMaterialInfoService.selectClosedListByCondition(dto);
        return R.ok(toPage(closedListVOS));
    }

    /**
     * 视频评价记录 列表
     */
    @GetMapping("/video-score-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "视频评价记录 列表")
    @RequiresPermissions(value = "system:evaluate:list")
    public R<PageInfo<VideoScoreListVO>> selectVideoScoreListByCondition(VideoScoreListDTO dto) {
        List<VideoScoreListVO> videoScoreListVOS = orderVideoFeedBackService.selectVideoScoreListByCondition(dto);
        return R.ok(toPage(videoScoreListVOS));
    }

    /**
     * 视频评价记录 拍摄模特下拉框
     */
    @GetMapping("/video-score-list-select-shoot-model")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "视频评价记录 拍摄模特下拉框")
    public R<List<ModelSelectVO>> videoScoreListSelectShootModel() {
        List<ModelSelectVO> modelInfoVOS = orderVideoFeedBackService.videoScoreListSelectShootModel();
        return R.ok(modelInfoVOS);
    }

    /**
     * 视频评价记录 评价人下拉框
     */
    @GetMapping("/video-score-list-select-evaluate-person")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "视频评价记录 评价人下拉框")
    public R<List<UserVO>> videoScoreListSelectEvaluatePerson() {
        List<UserVO> evaluatePersonSelect = orderVideoFeedBackService.videoScoreListSelectEvaluatePerson();
        return R.ok(evaluatePersonSelect);
    }

    /**
     * 运营帮模特上传素材
     */
    @PostMapping(value = "/back-help-model-upload-material")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营帮模特上传素材")
    @OrderRefundVerify(videoId = "#dto.videoId")
    @RequiresPermissions(value = {"order:manage:modelFeedbackMaterial","task:workOrder:modelFeedback"}, logical = Logical.OR)
    @OrderVideoOperate(operateType = OrderVideoOperateTypeEnum.CUSTOMER_SERVICE_FEEDBACK_MATERIAL, videoId = "#dto.videoId")
    public R<String> backHelpModelUploadMaterial(@RequestBody @Validated UploadLinkDTO dto) {
        orderVideoFeedBackMaterialService.uploadLink(dto);
        return R.ok();
    }

}
