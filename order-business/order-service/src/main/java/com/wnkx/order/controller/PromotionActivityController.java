package com.wnkx.order.controller;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.order.promotion.UpdatePromotionActivityDTO;
import com.ruoyi.system.api.domain.vo.order.promotion.BusinessParticipatoryActivityVO;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityAmendmentRecordVO;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO;
import com.wnkx.order.service.PromotionActivityAmendmentRecordService;
import com.wnkx.order.service.PromotionActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18 9:45
 */
@RestController
@RequestMapping("/promotion")
@Api(value = "优惠活动服务", tags = "优惠活动服务")
@RequiredArgsConstructor
public class PromotionActivityController extends BaseController {

    private final PromotionActivityService promotionActivityService;
    private final PromotionActivityAmendmentRecordService promotionActivityAmendmentRecordService;

    /**
     * 获取当前商家可参与的活动
     */
    @GetMapping("/get-business-participatory-activity")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取当前商家可参与的活动", response = BusinessParticipatoryActivityVO.class)
    public R<List<BusinessParticipatoryActivityVO>> getBusinessParticipatoryActivityVOS() {
        List<BusinessParticipatoryActivityVO> businessParticipatoryActivityVOS = promotionActivityService.getBusinessParticipatoryActivityVOS();
        return R.ok(businessParticipatoryActivityVOS);
    }

    /**
     * 获取是否需要提醒当前用户会员半价续费
     */
    @GetMapping("/get-remind-business-member-half-price-renewal")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "获取是否需要提醒当前用户会员半价续费")
    public R<Boolean> getRemindBusinessMemberHalfPriceRenewal() {
        Boolean remindBusinessMemberHalfPriceRenewal = promotionActivityService.getRemindBusinessMemberHalfPriceRenewal();
        return R.ok(remindBusinessMemberHalfPriceRenewal);
    }

    /**
     * 通过活动类型获取活动信息
     */
    @GetMapping("/get-promotion-activity-by-type")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "通过活动类型获取活动信息")
    @RequiresPermissions("merchant:manage:active")
    public R<PromotionActivityVO> getPromotionActivityByType(@RequestParam Integer type) {
        PromotionActivityVO promotionActivityVO = promotionActivityService.getPromotionActivityByType(type);
        return R.ok(promotionActivityVO);
    }

    /**
     * 通过活动类型获取修改记录
     */
    @GetMapping("/promotion-activity-amendment-record-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "通过活动类型获取修改记录")
    @RequiresPermissions(value = {"merchant:manage:active", "channel:fission:editMemberDiscount"}, logical = Logical.OR)
    public R<List<PromotionActivityAmendmentRecordVO>> selectPromotionActivityAmendmentRecordList(@RequestParam Integer type) {
        List<PromotionActivityAmendmentRecordVO> promotionActivityAmendmentRecordVOS = promotionActivityAmendmentRecordService.selectPromotionActivityAmendmentRecordList(type);
        return R.ok(promotionActivityAmendmentRecordVOS);
    }

    /**
     * 修改活动配置
     */
    @PutMapping("/update-promotion-activity")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改活动配置")
    @RequiresPermissions("merchant:manage:active")
    public R<Void> updatePromotionActivity(@RequestBody @Validated UpdatePromotionActivityDTO dto) {
        promotionActivityService.updatePromotionActivity(dto);
        return R.ok();
    }

    /**
     * 获取进行中的活动列表
     */
    @GetMapping("/get-valid-promotion-activity-list")
    @ApiOperation(value = "获取进行中的活动列表")
    public R<List<PromotionActivityVO>> getValidPromotionActivityList() {
        List<PromotionActivityVO> validPromotionActivityList = promotionActivityService.selectValidPromotionActivityList();
        return R.ok(validPromotionActivityList);
    }
}
