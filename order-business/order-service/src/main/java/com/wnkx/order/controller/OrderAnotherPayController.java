package com.wnkx.order.controller;

import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.business.balance.OnlineRechargeSubmitCredentialDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.pay.CreateAnotherPayLinkDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.order.OrderAnotherPay;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigInfo;
import com.ruoyi.system.api.domain.entity.order.PayeeAccountConfig;
import com.ruoyi.system.api.domain.vo.order.CreatePayVo;
import com.ruoyi.system.api.domain.vo.order.OrderPayInfoVO;
import com.wnkx.order.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/12/6 13:37
 */
@RestController
@RequestMapping("/another-pay")
@Api(value = "代付服务", tags = "代付服务")
@RequiredArgsConstructor
public class OrderAnotherPayController {

    private final OrderAnotherPayService orderAnotherPayService;
    private final OrderPayeeAccountConfigService orderPayeeAccountConfigService;

    private final PayService payService;
    private final IOrderService orderService;
    private final IPayeeAccountConfigService payeeAccountConfigService;
    private final OrderPayeeAccountConfigInfoService orderPayeeAccountConfigInfoService;

    /**
     * 创建或获取代付链接
     */
    @PostMapping("/create-link")
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE)
    @ApiOperation(value = "创建或获取代付链接")
    public R<String> createLink(@RequestBody @Validated CreateAnotherPayLinkDTO dto) {
        String code = orderAnotherPayService.createLink(dto);
        return R.ok(code);
    }

    /**
     * 通过code获取代付链接
     */
    @GetMapping("/get-by-code")
    @ApiOperation(value = "通过code获取代付链接")
    public R<OrderAnotherPay> getOrderAnotherPayByUUID(@RequestParam String code) {
        OrderAnotherPay validLinkByOrderNum = orderAnotherPayService.getOrderAnotherPayByUUID(code);
        return R.ok(validLinkByOrderNum);
    }

    /**
     * 通过 订单号或者合并单ID 获取代付链接
     */
    @GetMapping("/get-by-orderNum-or-mergeId")
    @ApiOperation(value = "通过 订单号或者合并单ID 获取代付链接")
    public R<OrderAnotherPay> getOrderAnotherPayByOrderNumOrMergeId(@RequestParam(required = false) String orderNum,
                                                                    @RequestParam(required = false) Long mergeId) {
        OrderAnotherPay validLinkByOrderNum = orderAnotherPayService.getOrderAnotherPayByOrderNumOrMergeId(orderNum, mergeId);
        return R.ok(validLinkByOrderNum);
    }

    /**
     * 取消代付
     */
    @GetMapping("/cancel")
    @ApiOperation(value = "取消代付")
    public R<String> cancel(@RequestParam String uuid) {
        orderAnotherPayService.cancel(uuid);
        return R.ok();
    }

    /**
     * 生成二维码
     */
    @PostMapping("/code")
    @ApiOperation(value = "生成二维码")
    public R<CreatePayVo> generateQrcode(@RequestBody @Validated PayCodeDTO payCodeDTO) {
        CreatePayVo qrcode = payService.generateQrcode(payCodeDTO, true);
        return R.ok(qrcode);
    }

    /**
     * 检查订单状态
     */
    @PostMapping("/check")
    @ApiOperation(value = "检查订单状态")
    public R<OrderPayStatusDTO> checkStatus(@RequestBody @Validated CheckStatusDTO checkStatusDTO) {
        OrderPayStatusDTO orderStatus = payService.checkAnotherPayStatus(checkStatusDTO);
        return R.ok(orderStatus);
    }

    /**
     * 订单支付页信息
     */
    @PostMapping(value = "/pay-info")
    @ApiOperation(value = "订单支付页信息")
    public R<OrderPayInfoVO> payInfo(@RequestBody PayInfoDTO payInfoDTO) {
        payInfoDTO.setIsAnother(true);
        OrderPayInfoVO orderPayInfoVO = payService.payInfo(payInfoDTO);
        return R.ok(orderPayInfoVO);
    }

    /**
     * 订单支付页信息
     */
    @GetMapping(value = "/pay-Member-info")
    @ApiOperation(value = "订单支付页信息")
    public R<OrderPayInfoVO> payMemberInfo(PayMemberInfoDTO dto) {
        OrderPayInfoVO orderPayInfoVO = payService.payMemberInfo(dto, true);
        return R.ok(orderPayInfoVO);
    }

    /**
     * 提交凭证信息
     */
    @PostMapping(value = "/submit-credential")
    @ApiOperation(value = "提交凭证信息")
    public R<String> submitCredential(@RequestBody @Validated SubmitCredentialDTO submitCredentialDTO) {
        orderService.submitCredential(submitCredentialDTO, true);
        return R.ok();
    }

    @PostMapping(value = "/businessBalancePrepay/submit-credential")
    @ApiOperation(value = "提交凭证信息")
    public R<String> onlineSubmitCredential(@RequestBody @Validated OnlineRechargeSubmitCredentialDTO submitCredentialDTO) {
        orderAnotherPayService.onlineSubmitCredential(submitCredentialDTO);
        return R.ok();
    }

    @GetMapping("/businessBalancePrepay/getOnlineeDetail")
    @ApiOperation(value = "获取充值详情")
    public R<BusinessBalancePrepay> onlineDetail(@RequestParam String orderNum) {
        return R.ok(orderAnotherPayService.getOnlineDetailByOrderNum(orderNum));
    }

    /**
     * 根据账号类型获取收款人账号配置表
     */
    @GetMapping("/getValidConfig")
    @ApiOperation(value = "根据账号类型获取收款人账号配置表")
    public R<PayeeAccountConfig> getValidConfig(Integer type) {
        return R.ok(payeeAccountConfigService.getValidConfigByAccountType(type));
    }

    /**
     * 下单锁定
     */
    @PostMapping(value = "/payLock")
    @ApiOperation(value = "下单锁定")
    public R<String> payLock(@RequestBody @Valid OrderPayLockDTO dto) {
        orderService.anotherPayLock(dto);
        return R.ok();
    }

    /**
     * 获取当前生效的收款人信息(根据类型)
     */
    @GetMapping("/payee/type/{type}")
    @ApiOperation(value = "获取当前生效的收款人信息(根据类型)")
    public R<OrderPayeeAccountConfigInfoDTO> getCurrentActivePayeeInfo(@PathVariable("type") Integer type) {
        return R.ok(orderPayeeAccountConfigService.typeInfo(type));
    }

    /**
     * 获取收款人信息(根据id)
     */
    @GetMapping("/payee/info/{id}")
    @ApiOperation(value = "获取收款人信息(根据id)")
    public R<OrderPayeeAccountConfigInfoDTO> getPayeeInfoById(@PathVariable("id") Long id) {
        return R.ok(orderPayeeAccountConfigService.getPayeeInfoById(id));
    }

    /**
     * 获取收款人信息(根据id)
     */
    @GetMapping("/payee/info/detail/{id}")
    @ApiOperation(value = "获取收款人信息(根据id)")
    public R<OrderPayeeAccountConfigInfo> getPayeeInfoDetailById(@PathVariable("id") Long id) {
        return R.ok(orderPayeeAccountConfigInfoService.getById(id));
    }


    /**
     * 下载请款清单
     */
    @PostMapping(value = "/download-pay-info")
    @ApiOperation(value = "下载请款清单")
    public void downloadPayInfo(
            @ApiParam("合并单ID") @RequestParam(required = false) Long mergeId,
            @ApiParam("代付CODE") @RequestParam String code,
            HttpServletResponse response
    ) throws IOException {
        OrderAnotherPay orderAnotherPay = orderAnotherPayService.getOrderAnotherPayByUUID(code);
        Assert.notNull(orderAnotherPay, "订单不存在");
        payService.downloadPayInfo(mergeId, orderAnotherPay.getOrderNum(), true, response);
    }
}
