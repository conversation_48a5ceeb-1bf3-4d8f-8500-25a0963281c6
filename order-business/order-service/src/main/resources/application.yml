server:
  port: 7011
  shutdown: graceful
spring:
  application:
    name: order-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${wnkx.ds.ip}:${wnkx.ds.port}/${wnkx.ds.order-db:order-center}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${wnkx.ds.username}
    password: ${wnkx.ds.password}
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.wnkx.order.domain
  global-config:
    db-config:
      id-type: auto

# swagger配置
swagger:
  title: 订单模块接口文档   #标题
  description: 订单模块接口文档   #描述
  version: 1.0.0  #版本
  contact:
    name: wnkx   #作者
