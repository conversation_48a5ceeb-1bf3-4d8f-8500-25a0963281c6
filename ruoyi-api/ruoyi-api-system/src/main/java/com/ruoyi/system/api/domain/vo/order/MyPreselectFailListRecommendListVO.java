package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/25 15:19
 */
@Data
public class MyPreselectFailListRecommendListVO implements Serializable {
    private static final long serialVersionUID = -2141022883694372290L;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long matchId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private Integer modelType;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer shootingCountry;

    /**
     * 是否照顾单
     */
    @ApiModelProperty(value = "是否照顾单（0=否,1=是）")
    private Integer isCare;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private Integer isGund;

    /**
     * 是否是相同产品链接的订单
     */
    @ApiModelProperty(value = "是否是相同产品链接的订单")
    private Boolean isSameProductLink;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    /**
     * 参考图片（关联资源id）
     */
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 意向模特id
     */
    @JsonIgnore
    private Long intentionModelId;

    /**
     * 意向模特名称
     */
    @ApiModelProperty(value = "意向模特名称")
    private String intentionModelName;

    /**
     * 注意事项图片（FK:order_resource.id，多个用,隔开）
     */
    @JsonIgnore
    private String cautionsPicId;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    private List<OrderVideoContent> shootRequired;

    /**
     * 产品卖点
     */
    @ApiModelProperty(value = "产品卖点")
    private String sellingPointProduct;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    private OrderVideoCautionsVO orderVideoCautionsVO;

    /**
     * 预选模特数量
     */
    @ApiModelProperty(value = "预选模特数量")
    private Integer preselectModelCount;
}
