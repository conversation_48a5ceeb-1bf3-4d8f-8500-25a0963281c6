package com.ruoyi.system.api.domain.vo.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单反馈(模特)_详细信息对象 order_video_feed_back_material_info
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderFeedBackMaterialInfoVO implements Serializable {

    private static final long serialVersionUID = 6570514412729009110L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 素材id（FK:order_video_feed_back_material.id）
     */
    @NotNull(message = "[素材id]不能为空")
    @ApiModelProperty(value = "素材id")
    private Long materialId;

    /**
     * 链接
     */
    @NotNull(message = "[链接]不能为空")
    @ApiModelProperty(value = "链接")
    private String link;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /**
     * 上传对象（2:运营,3:模特）
     */
    @ApiModelProperty(value = "上传对象（2:运营,3:模特）")
    private Integer object;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    @JsonIgnore
    private Long userId;

    /**
     * 上传用户（运营）
     */
    @ApiModelProperty(value = "上传用户（运营）")
    private UserVO back;

    /**
     * 上传用户（模特）
     */
    @ApiModelProperty(value = "上传用户（模特）")
    private ModelInfoVO model;
}
