package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/3 10:56
 */
@Data
public class OrderModelMyOrderListVO implements Serializable {
    private static final long serialVersionUID = 7460035650344863432L;
    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long id;
    /**
     * 订单状态（6:Pending completion,8:Completed,9:Seller Cancellation）
     */
    @ApiModelProperty(value = "订单状态（6:Pending completion,8:Completed,9:Seller Cancellation）")
    private Integer status;
    /**
     * 是否有产生售后（非0表示有售后）
     */
    @ApiModelProperty(value = "是否有产生售后（非0表示有售后）")
    private Integer inAfterSale;
    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 视频风格
     */
    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    @JsonIgnore
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    @JsonIgnore
    private Integer refundPicCount;

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 实物（0:是,1:不是）
     */
    @ApiModelProperty(value = "实物（0:是,1:不是）")
    private Integer isObject;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号")
    @JsonIgnore
    private String number;

    /**
     * 物流主状态
     */
    @ApiModelProperty(value = "物流主状态")
    private String mainStatus;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "UTC+0")
    private Date signTime;

    /**
     * 订单进入新状态的时间
     */
    @ApiModelProperty(value = "订单进入新状态的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "UTC+0")
    private Date statusTime;
}
