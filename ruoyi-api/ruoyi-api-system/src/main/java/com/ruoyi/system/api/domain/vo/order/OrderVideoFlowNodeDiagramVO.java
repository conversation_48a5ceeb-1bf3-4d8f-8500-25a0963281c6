package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Data
public class OrderVideoFlowNodeDiagramVO implements Serializable {

    private static final long serialVersionUID = 5925833649883516815L;
    /**
     * 当前节点（1:下单支付,2:匹配模特,3:商家发货,4:完成拍摄,5:商家确认,6:订单完成,10:取消订单）
     */
    @ApiModelProperty(value = "当前节点（1:下单支付,2:匹配模特,3:商家发货,4:完成拍摄,5:商家确认,6:订单完成,10:取消订单）")
    private Integer node;

    /**
     * 节点完成时间
     */
    @ApiModelProperty(value = "节点完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date time;
}
