package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :应收审核导出
 * @create :2024-09-11 11:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableAuditListExportVO implements Serializable {
    private static final long serialVersionUID = 6585120626848838161L;

    @Excel(name = "订单号")
    private String orderNum;

    @Excel(name = "支付号")
    private String payNum;

    @Excel(name = "商家名称")
    private String businessName;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "下单运营")
    private String orderUserName;

    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @Excel(name = "提交审批", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitCredentialTime;

    @Excel(name = "付款账号", defaultValue = StrPool.DASHED)
    private String payAccount;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额")
    private Integer payType;

    @Excel(name = "收款账号")
    private String orderPayeeAccount;

    @Excel(name = "支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @Excel(name = "百度汇率")
    private BigDecimal currentExchangeRate;

    @Excel(name = "支付金额（单位：￥）")
    private BigDecimal payAmount;

    /**
     * 满5单减100
     */
    @Excel(name = "限时满减活动", defaultValue = StrPool.DASHED)
    private BigDecimal fullConcession;

    /**
     * 每月首单立减优惠方案
     */
    @Excel(name = "每月首单立减优惠方案", defaultValue = StrPool.DASHED)
    private String monthFirstOrderDiscount;

    /**
     * 每月首单立减金额
     */
    @Excel(name = "每月首单立减金额", defaultValue = StrPool.DASHED)
    private BigDecimal monthFirstOrderDiscountActivity;

    @Excel(name = "钱包抵扣")
    private BigDecimal useBalance;

    @Excel(name = "剩余支付金额")
    private BigDecimal surplusAmount;

    @Excel(name = "实付金额", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmountCurrency;

    @Excel(name = "币种", defaultValue = StrPool.DASHED)
    private String currency;

    @Excel(name = "实付人民币", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmount;

//    @Excel(name = "差额", defaultValue = StrPool.DASHED)
    private BigDecimal differenceAmount;

    @Excel(name = "差额", defaultValue = StrPool.DASHED)
    private String differenceAmountString;

    @Excel(name = "审核人")
    private String auditUserName;

    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=审核通过,2=审核异常,3=已关闭")
    private Integer auditStatus;

    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Excel(name = "备注内容")
    private String remark;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @Excel(name = "全币种支付类型", readConverterExp = "701=其他平台/银行,702=万里汇", defaultValue = StrPool.DASHED)
    private Integer payTypeDetail;

}
