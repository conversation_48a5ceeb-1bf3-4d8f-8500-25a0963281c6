package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/24 10:37
 */
@Data
public class OrderVideoRejectRecordVO implements Serializable {
    private static final long serialVersionUID = -4388546102177746279L;

    /**
     * 预选模特ID
     */
    @ApiModelProperty(value = "预选模特ID")
    private Long preselectModelId;

    /**
     * 驳回时间
     */
    @ApiModelProperty(value = "驳回时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date oustTime;

    /**
     * 操作运营姓名
     */
    @ApiModelProperty(value = "操作运营姓名")
    private String oustOperatorName;

    /**
     * 操作运营微信名
     */
    @ApiModelProperty(value = "操作运营微信名")
    private String oustOperatorNickName;

    /**
     * 新意向模特姓名
     */
    @ApiModelProperty(value = "新意向模特姓名")
    private String intentionModelName;

    /**
     * 新意向模特账号
     */
    @ApiModelProperty(value = "新意向模特账号")
    private String intentionModelAccount;

    /**
     * 驳回模特姓名
     */
    @ApiModelProperty(value = "驳回模特姓名")
    private String rejectModelName;

    /**
     * 驳回模特账号
     */
    @ApiModelProperty(value = "驳回模特账号")
    private String rejectModelAccount;

    /**
     * 驳回原因
     */
    @ApiModelProperty(value = "驳回原因")
    private String remark;
}
