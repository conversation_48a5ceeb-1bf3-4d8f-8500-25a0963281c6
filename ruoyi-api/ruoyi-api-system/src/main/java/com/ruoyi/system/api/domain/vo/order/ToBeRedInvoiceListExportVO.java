package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/7 20:54
 */
@Data
public class ToBeRedInvoiceListExportVO implements Serializable {
    private static final long serialVersionUID = -3387101751068173404L;

    /**
     * 申票码
     */
    @Excel(name = "申票码")
    private String ticketCode;

    /**
     * 发票号
     */
    @Excel(name = "红冲发票号")
    private String number;


    @Excel(name = "订单类型", readConverterExp = "0=视频订单,1=会员订单,5=线上钱包充值")
    private Integer orderType;

    @Excel(name = "单号")
    private String videoCodeStr;

    /**
     * 商家编码
     */
    @Excel(name = "商家编码")
    private String merchantCode;

    /**
     * 开票信息
     */
    @Excel(name = "开票信息")
    private String invoiceInfo;

    /**
     * 开票金额
     */
    @Excel(name = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 红冲原因（1：重开发票，2：商家提现）
     */
    @Excel(name = "红冲原因", readConverterExp = "1=重开发票,2=商家提现")
    private Integer invoiceRedCause;

    /**
     * 申请时间
     */
    @Excel(name = "发起时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date applyTime;

    /**
     * 红冲状态（1：待红冲，2：不红冲，3：已红冲）
     */
    @Excel(name = "红冲状态", readConverterExp = "1=待红冲,2=不红冲,3=已红冲")
    private Integer invoiceRedStatus;
}
