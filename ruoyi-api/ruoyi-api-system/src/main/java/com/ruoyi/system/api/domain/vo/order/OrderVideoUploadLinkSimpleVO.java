package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Data
public class OrderVideoUploadLinkSimpleVO implements Serializable {
    private static final long serialVersionUID = 7164021179924432771L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    /**
     * 视频封面图URI
     */
    @ApiModelProperty(value = "视频封面图URI")
    private String videoCover;

    /**
     * 提交信息对象（1:商家,2:运营）
     */
    @ApiModelProperty(value = "提交信息对象（1:商家,2:运营）")
    private Integer object;

    /**
     * 提交信息用户id
     */
    @ApiModelProperty(value = "提交信息用户id")
    @JsonIgnore
    private Long userId;

    /**
     * 提交信息用户（商家）
     */
    @ApiModelProperty(value = "提交信息用户（商家）")
    private BusinessAccountVO company;

    /**
     * 提交信息用户（运营）
     */
    @ApiModelProperty(value = "提交信息用户（运营）")
    private UserVO back;

    /**
     * 需要上传的链接
     */
    @ApiModelProperty(value = "需要上传的链接")
    private String needUploadLink;

    /**
     * 提交信息时间
     */
    @ApiModelProperty(value = "提交信息时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date time;

    /**
     * 上传的链接
     */
    @ApiModelProperty(value = "上传的链接")
    private String uploadLink;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    @JsonIgnore
    private Long uploadUserId;

    /**
     * 上传用户
     */
    @ApiModelProperty(value = "上传用户")
    private UserVO uploadUser;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadTime;

    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String remark;

    /**
     * 上传账号
     */
    @ApiModelProperty(value = "上传账号")
    private String uploadAccount;

    /**
     * 操作备注
     */
    @ApiModelProperty(value = "操作备注")
    private String operateRemark;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）")
    private Integer status;
}
