package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16 17:55
 */
@Data
public class MyPreselectDistributionListVO implements Serializable {
    private static final long serialVersionUID = 744903546743459155L;

    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    private Long modelId;

    @JsonIgnore
    private String ovmpmIds;

    /**
     * 模特图片URI
     */
    @ApiModelProperty(value = "模特图片URI")
    private String modelPic;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型", notes = "0:影响者,1:素人")
    private Integer type;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度（0:一般模特,1:优质模特,2:中度模特）", notes = "0:一般模特,1:优质模特,2:中度模特")
    private Integer cooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)")
    private BigDecimal cooperationScore;

    /**
     * 待拍数
     */
    @ApiModelProperty(value = "待拍数")
    private Long waits;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 售后率
     */
    @ApiModelProperty(value = "售后率")
    private BigDecimal afterSaleRate;

    /**
     * 模特剩余可携带订单数
     */
    @ApiModelProperty(value = "模特剩余可携带订单数")
    private Long carryCount;

    /**
     * 英文部客服名称
     */
    @ApiModelProperty(value = "英文部客服名称")
    private String englishServiceName;

    /**
     * 分发订单列表
     */
    @ApiModelProperty(value = "分发订单列表")
    private List<DistributionOrderListVO> distributionOrderListVOS;
}
