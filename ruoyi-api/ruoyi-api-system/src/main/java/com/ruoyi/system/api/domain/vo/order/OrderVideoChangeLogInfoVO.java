
package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:05
 */
@Data
public class OrderVideoChangeLogInfoVO implements Serializable {
    private static final long serialVersionUID = -1665170042795243788L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 变更记录id (FK:order_video_change_log.id)
     */
    @ApiModelProperty("变更记录id")
    private Long changeLogId;
    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    private String fieldName;

    /**
     * 当前值
     */
    @ApiModelProperty("当前值")
    private Object value;
}
