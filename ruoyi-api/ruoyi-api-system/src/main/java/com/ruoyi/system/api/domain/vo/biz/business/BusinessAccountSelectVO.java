package com.ruoyi.system.api.domain.vo.biz.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商家账户下拉框对象
 * <AUTHOR>
 * @date 2022/9/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessAccountSelectVO implements Serializable {
    private static final long serialVersionUID = -7866989729901438405L;
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("微信昵称")
    private String nickName;
}