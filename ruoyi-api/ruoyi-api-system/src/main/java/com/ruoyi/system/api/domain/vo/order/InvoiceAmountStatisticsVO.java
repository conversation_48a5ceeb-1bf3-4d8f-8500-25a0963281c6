package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/11 9:58
 */
@Data
public class InvoiceAmountStatisticsVO implements Serializable {
    private static final long serialVersionUID = -654834846819936326L;

    /**
     * 待开票金额
     */
    @ApiModelProperty(value = "待开票金额")
    private BigDecimal amountToBeBilled;

    /**
     * 待红冲金额
     */
    @ApiModelProperty(value = "待红冲金额")
    private BigDecimal amountToBeFlushed;

    /**
     * 已开票金额
     */
    @ApiModelProperty(value = "已开票金额")
    private BigDecimal invoicedAmount;
}
