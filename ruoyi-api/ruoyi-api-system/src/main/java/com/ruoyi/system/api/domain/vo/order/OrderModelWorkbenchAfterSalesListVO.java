package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/3 10:56
 */
@Data
public class OrderModelWorkbenchAfterSalesListVO implements Serializable {
    private static final long serialVersionUID = -7593229478149256281L;
    /**
     * 售后单id（反馈素材id）
     */
    @ApiModelProperty(value = "售后单id（反馈素材id）")
    private Long id;
    /**
     * 驳回标题
     */
    @ApiModelProperty(value = "驳回标题")
    private String title;
    /**
     * 模特是否回复被驳回的素材（0:已回复,1:未回复）
     */
    @ApiModelProperty(value = "模特是否回复被驳回的素材（0:已回复,1:未回复）")
    private Integer replyStatus;
    /**
     * 售后时间
     */
    @ApiModelProperty(value = "售后时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "UTC+0")
    private Date updateTime;
    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;
    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    @JsonIgnore
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    @JsonIgnore
    private Integer refundPicCount;

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;
}
