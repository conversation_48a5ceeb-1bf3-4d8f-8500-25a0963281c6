package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.system.api.domain.vo.biz.model.AddPreselectModelListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单_视频_预选模特VO对象
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@ApiModel(value = "订单_视频_预选模特VO对象")
@Data
public class OrderVideoMatchPreselectModelVO implements Serializable {
    private static final long serialVersionUID = 1243783057544703793L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long matchId;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）
     */
    @ApiModelProperty(value = "添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发,5:排单推荐,6:凑单推荐）")
    private Integer addType;

    /**
     * 添加人id
     */
    @ApiModelProperty(value = "添加人id")
    @Excel(name = "添加人id")
    private Long addUserId;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date addTime;

    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    @JsonIgnore
    private Long modelId;

    /**
     * 模特
     */
    @ApiModelProperty(value = "模特")
    private AddPreselectModelListVO model;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型(0:影响者,1:素人)")
    private Integer modelType;

    /**
     * 模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private String modelPlatform;

    /**
     * 模特合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "模特合作深度(0:一般模特,1:优质模特,2:中度模特)")
    private Integer modelCooperation;

    /**
     * 模特评分 (0.0-10.0)
     */
    @ApiModelProperty(value = "模特评分 (0.0-10.0)", notes = "模特评分 (0.0-10.0)", required = true)
    private BigDecimal modelCooperationScore;

    /**
     * 模特对接人名称
     */
    @ApiModelProperty(value = "模特对接人名称")
    private String modelPersonName;

    /**
     * 状态（0:未对接,1:已对接,2:已选定,3:已淘汰）
     */
    @ApiModelProperty(value = "状态（0:未对接,1:已对接,2:已选定,3:已淘汰）", notes = "0:未对接,1:已对接,2:已选定,3:已淘汰")
    private Integer status;

    /**
     * 选择时间
     */
    @ApiModelProperty(value = "选择时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date selectedTime;

    /**
     * 模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)
     */
    @ApiModelProperty(value = "模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)")
    private Integer modelIntention;

    /**
     * 淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向,6:订单回退,7:合作状态变更,8:暂停匹配,9:交易关闭）
     */
    @ApiModelProperty(value = "淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向,6:订单回退,7:合作状态变更,8:暂停匹配,9:交易关闭）")
    @Excel(name = "淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向,6:订单回退,7:合作状态变更,8:暂停匹配,9:交易关闭）")
    private Integer oustType;

    /**
     * 淘汰时间
     */
    @ApiModelProperty(value = "淘汰时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date oustTime;

    /**
     * 确认淘汰时间
     */
    @ApiModelProperty(value = "确认淘汰时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date confirmOustTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 图片存储连接
     */
    @ApiModelProperty(value = "图片存储链接")
    private String objectKey;

    /**
     * 是否已在最新匹配单的预选模特列表里 true=是
     */
    @ApiModelProperty(value = "是否已在最新匹配单的预选模特列表里 true=是")
    private boolean isActive;

    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    @ApiModelProperty(value = "创建订单登录账号ID")
    private Long bizUserId;

    /**
     * 分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）
     */
    @ApiModelProperty(value = "分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）")
    private Integer distributionResult;

    /**
     * 拍摄模特注意事项
     */
    @ApiModelProperty(value = "拍摄模特注意事项")
    private String shootAttention;

    /**
     * 拍摄注意事项对象存储键值
     */
    @ApiModelProperty("拍摄注意事项对象存储键值")
    private String shootAttentionObjectKey;
}
