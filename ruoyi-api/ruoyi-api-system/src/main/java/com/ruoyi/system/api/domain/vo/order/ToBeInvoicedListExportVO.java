package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/7 11:05
 */
@Data
public class ToBeInvoicedListExportVO implements Serializable {
    private static final long serialVersionUID = -4719710781300548134L;

    /**
     * 申票码
     */
    @Excel(name = "申票码")
    private String ticketCode;

    /**
     * 包含订单
     */
    @Excel(name = "包含订单")
    private String orderNumStr;

    /**
     * 商家编码
     */
    @Excel(name = "商家编码")
    private String merchantCode;

    /**
     * 开票金额
     */
    @Excel(name = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 提交时间
     */
    @Excel(name = "发起时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 来源（1：商家申请，2：红冲重开）
     */
    @Excel(name = "来源", readConverterExp = "1=商家申请,2=红冲重开")
    private Integer source;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @Excel(name = "发票类型", readConverterExp = "1=增值税普通发票,2=形式发票")
    private Integer invoiceType;

    /**
     * 订单类型（0:视频订单,1:会员订单）
     */
    @Excel(name = "订单类型", readConverterExp = "0=视频订单,1=会员订单,5=线上钱包充值")
    private Integer orderType;

    /**
     * 开票信息
     */
    @Excel(name = "开票信息")
    private String invoiceInfo;

    /**
     * 开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）
     */
    @Excel(name = "开票状态", readConverterExp = "0=开票信息待完善,1=待开票,2=待确认,3=已投递,4=已作废,5=待审核,6:已取消,7=已投递")
    private Integer status;
}
