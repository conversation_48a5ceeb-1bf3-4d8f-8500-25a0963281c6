package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20 17:55
 */
@Data
public class SelectHistoryEditListVO implements Serializable {
    private static final long serialVersionUID = -8520719061219757641L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 回退ID
     */
    @ApiModelProperty(value = "回退ID")
    private Long rollbackId;

    /**
     * 素材提交人姓名
     */
    @ApiModelProperty(value = "素材提交人姓名")
    private String uploadBy;

    /**
     * 素材提交人ID
     */
    @ApiModelProperty(value = "素材提交人ID")
    @JsonIgnore
    private Long uploadById;

    /**
     * 素材提交时间
     */
    @ApiModelProperty(value = "素材提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private String uploadTime;

    /**
     * 领取编码
     */
    @ApiModelProperty(value = "领取编码")
    private String getCode;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;

    /**
     * 领取人姓名
     */
    @ApiModelProperty(value = "领取人姓名")
    private String getBy;

    /**
     * 领取人ID
     */
    @ApiModelProperty(value = "领取人ID")
    @JsonIgnore
    private Long getById;

    /**
     * 领取时间
     */
    @ApiModelProperty(value = "领取时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date getTime;

    /**
     * 剪辑人姓名
     */
    @ApiModelProperty(value = "剪辑人姓名")
    private String editBy;

    /**
     * 剪辑人ID
     */
    @ApiModelProperty(value = "剪辑人ID")
    @JsonIgnore
    private Long editById;

    /**
     * 剪辑时间
     */
    @ApiModelProperty(value = "剪辑时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date editTime;

    /**
     * 反馈人ID
     */
    @ApiModelProperty(value = "反馈人ID")
    @JsonIgnore
    private Long feedbackById;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    @JsonIgnore
    private Date feedbackTime;

    /**
     * 反馈给商家素材详情
     */
    @ApiModelProperty(value = "反馈给商家素材详情")
    private List<SelectHistoryEditListVOFeedBackVO> feedBackVOS;

    /**
     * 反馈状态（1：已反馈，2：不反馈给商家，4：联动关闭）
     */
    @ApiModelProperty(value = "反馈状态（1：已反馈，2：不反馈给商家，4：联动关闭）")
    private Integer feedbackStatus;

    /**
     * 反馈状态备注
     */
    @ApiModelProperty(value = "反馈状态备注")
    private String feedbackRemark;
}
