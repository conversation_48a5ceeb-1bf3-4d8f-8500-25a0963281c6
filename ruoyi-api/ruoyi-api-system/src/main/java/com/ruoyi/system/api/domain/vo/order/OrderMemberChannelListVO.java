package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
public class OrderMemberChannelListVO implements Serializable {
    private static final long serialVersionUID = 3446755200690923362L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 渠道ID
     */
    @ApiModelProperty("渠道ID")
    private Long channelId;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String channelName;

    /**
     * 渠道手机号
     */
    @ApiModelProperty("渠道手机号")
    private String channelPhone;

    /**
     * 商家id
     */
    @JsonIgnore
    private Long businessId;

    /**
     * 商家名称
     */
    @ApiModelProperty("商家名称")
    private String businessName;

    /**
     * 商家端登录用户微信昵称
     */
    @ApiModelProperty("商家端登录用户微信昵称")
    private String bizUserNickName;

    /**
     * 商家端登录用户手机号
     */
    @ApiModelProperty("商家端登录用户手机号")
    private String bizUserPhone;

    @ApiModelProperty("商家端登录用户手机号")
    private Long bizUserId;

    /**
     * 会员编码
     */
    @ApiModelProperty("会员编码")
    private String memberCode;

    /**
     * 会员类型
     */
    @ApiModelProperty("会员类型：0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    /**
     * 购买时间
     */
    @ApiModelProperty("购买时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;


    @ApiModelProperty("结算类型（1-固定金额，2-固定比例）")
    private Integer settleType;

    @ApiModelProperty("结算比例")
    private BigDecimal settleRage;

    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty("实际结算金额")
    private BigDecimal realSettleAmount;

    /**
     * 结算时间
     */
    @ApiModelProperty("结算时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date settleTime;

    /**
     * 状态
     */
    @ApiModelProperty("状态（0-未结算，1-已结算）")
    private Integer settleStatus;


    public void setBizUserPhone(String bizUserPhone) {
        this.bizUserPhone = bizUserPhone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    @ApiModelProperty("打款时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payoutTime;
}
