package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单详情（售后状态）
 *
 * <AUTHOR>
 * @date 2024/7/2 9:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderModelAfterSalesInfoVO extends OrderModelDetailInfoVO {
    private static final long serialVersionUID = -6324270710949559957L;
    /**
     * 售后单id（素材id）
     */
    @ApiModelProperty(value = "售后单id（素材id）")
    private Long materialId;
    /**
     * 驳回标题
     */
    @ApiModelProperty(value = "驳回标题")
    private String title;

    /**
     * 驳回理由
     */
    @ApiModelProperty(value = "驳回理由")
    private String remark;

    /**
     * 模特是否回复被驳回的素材（0:已回复,1:未回复）
     */
    @ApiModelProperty("模特是否回复被驳回的素材（0:已回复,1:未回复）")
    private Integer replyStatus;

    /**
     * 售后时间
     */
    @ApiModelProperty(value = "售后时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "UTC+0")
    private Date updateTime;
}
