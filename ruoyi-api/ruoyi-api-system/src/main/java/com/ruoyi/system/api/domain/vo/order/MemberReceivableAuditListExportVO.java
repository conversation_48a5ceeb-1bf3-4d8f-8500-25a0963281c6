package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员应收审核导出
 * @create :2024-09-11 11:25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberReceivableAuditListExportVO implements Serializable {
    private static final long serialVersionUID = 6585120626848838161L;

    @Excel(name = "会员订单号")
    private String orderNum;

    @Excel(name = "商家名称")
    private String businessName;

    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

//    @Excel(name = "商家微信昵称")
    private String nickName;

    private String businessAccount;

    @Excel(name = "提交审批", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitCredentialTime;

    @Excel(name = "付款账号", defaultValue = StrPool.DASHED)
    private String payAccount;

    @Excel(name = "支付方式", readConverterExp = "1=微信,2=支付宝,3=云闪付/银联,4=数字人民币,5=银行,6=对公,7=全币种,10=余额,11=微信+余额,12=支付宝+余额,13=云闪付/银联+余额,14=数字人民币+余额,15=银行+余额,16=对公+余额,17=全币种+余额", defaultValue = "-")
    private Integer payType;

    @Excel(name = "收款账号")
    private String orderPayeeAccount;


    @Excel(name = "套餐类型", readConverterExp = "0=季度会员,1=一年会员,2=三年会员")
    private Integer packageType;

    private BigDecimal payAmountDollar;

    @Excel(name = "套餐金额（$）")
    private BigDecimal packageAmount;

    @Excel(name = "百度汇率", defaultValue = StrPool.DASHED)
    private BigDecimal currentExchangeRate;

    @Excel(name = "套餐金额（￥）")
    private BigDecimal orderAmount;

    @Excel(name = "钱包抵扣")
    private BigDecimal useBalance;

    @Excel(name = "剩余支付")
    private BigDecimal surplusAmount;

    @Excel(name = "实付金额", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmountCurrency;

    @Excel(name = "币种", defaultValue = StrPool.DASHED)
    private String currency;

    @Excel(name = "实付人民币", defaultValue = StrPool.DASHED)
    private BigDecimal realPayAmount;

//    @Excel(name = "差额", defaultValue = StrPool.DASHED)
    private BigDecimal differenceAmount;

    @Excel(name = "差额", defaultValue = StrPool.DASHED)
    private String differenceAmountString;

    //    @Excel(name = "种草优惠")
    private BigDecimal seedCodeDiscount;

    @Excel(name = "审核人")
    private String auditUserName;

    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=审核通过,2=审核异常,3=已关闭")
    private Integer auditStatus;

    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Excel(name = "备注")
    private String orderRemark;

    /**
     * 支付方式明细（701：全币种-其他平台/银行支付，702：全币种-万里汇）
     */
    @Excel(name = "全币种支付类型", readConverterExp = "701=其他平台/银行,702=万里汇", defaultValue = StrPool.DASHED)
    private Integer payTypeDetail;

}
