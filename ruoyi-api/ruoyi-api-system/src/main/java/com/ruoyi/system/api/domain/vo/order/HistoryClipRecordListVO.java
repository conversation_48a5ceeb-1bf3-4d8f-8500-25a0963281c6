package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:54
 */
@Data
public class HistoryClipRecordListVO implements Serializable {
    private static final long serialVersionUID = -3364745874771679244L;

    /**
     * 回退ID
     */
    @ApiModelProperty(value = "回退ID")
    private Long rollbackId;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    @JsonIgnore
    private Long submitById;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitBy;

    /**
     * 提交人时间
     */
    @ApiModelProperty(value = "提交人时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 任务单类型（1：售后单，2：工单）
     */
    @ApiModelProperty(value = "任务单类型（1：售后单，2：工单）")
    private Integer taskType;

    /**
     * 售后分类（1：视频，2：照片）
     */
    @ApiModelProperty(value = "售后分类（1：视频，2：照片）")
    private Integer afterSaleClass;

    /**
     * 售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）
     */
    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）")
    private Integer afterSaleVideoType;

    /**
     * 售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）
     */
    @ApiModelProperty(value = "售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）")
    private Integer afterSalePicType;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private Integer workOrderType;

    /**
     * 是否是初始剪辑要求（1：是，0：不是）
     */
    @ApiModelProperty(value = "是否是初始剪辑要求（1：是，0：不是）")
    private Integer isInitial;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 问题图片（FK:order_resource.id）
     */
    @ApiModelProperty(value = "问题图片")
    @JsonIgnore
    private String issuePicId;

    /**
     * 问题图片（FK:order_resource.id）
     */
    @ApiModelProperty(value = "问题图片")
    private List<String> issuePic = new ArrayList<>();
}
