package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单退款统计
 * @create :2024-11-15 14:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVideoRefundStatisticsVO implements Serializable {
    private static final long serialVersionUID = -4805195240636100633L;

    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    @ApiModelProperty(value = "补偿金额统计")
    private BigDecimal reparationAmount;

    @ApiModelProperty(value = "取消订单金额统计")
    private BigDecimal cancelOrderAmount;

    @ApiModelProperty(value = "取消选配金额统计")
    private BigDecimal cancelOpentionAmount;
}
