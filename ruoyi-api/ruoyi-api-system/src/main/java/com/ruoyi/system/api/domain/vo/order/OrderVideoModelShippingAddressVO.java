package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/9 18:26
 */
@Data
public class OrderVideoModelShippingAddressVO implements Serializable {
    private static final long serialVersionUID = 7276918055042810327L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty("视频id")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 模特id (FK:order_video.shoot_model_id)
     */
    @ApiModelProperty("模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 模特
     */
    @ApiModelProperty("模特")
    private ModelInfoVO shootModel;

    /**
     * 国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty("国家")
    private Integer nation;

    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    private String recipient;

    /**
     * 城市
     */
    @ApiModelProperty("城市")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty("州")
    private String state;

    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    private String zipcode;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String detailAddress;

    /**
     * 发货备注
     */
    @ApiModelProperty("发货备注")
    private String shippingRemark;

    /**
     * 发货备注图片（FK:order_resource.id，多个用,隔开）
     */
    @ApiModelProperty("发货备注图片")
    @JsonIgnore
    private String shippingPic;

    /**
     * 发货图片
     */
    @ApiModelProperty(value = "发货图片")
    private List<String> shippingPics = new ArrayList<>();

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 手机号是否可见(0-不可见,1-可见)
     */
    @ApiModelProperty(value = "手机号是否可见(0-不可见,1-可见)")
    private Integer phoneVisible;

    /**
     * 标记物流状态（1:标记发货）
     */
    @ApiModelProperty(value = "标记物流状态（1:标记发货）")
    private Integer logisticFlag;

    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货原因")
    private String logisticFlagRemark;

    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date logisticFlagTime;
}
