package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberChannelStatisticVO implements Serializable {

    private static final long serialVersionUID = 8917931417478512384L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 会员成交数
     */
    @ApiModelProperty("会员成交数")
    private Integer memberNum;

    /**
     * 待结算金额
     */
    @ApiModelProperty("待结算金额")
    private BigDecimal unSettleAmount;

    /**
     * 已结算金额
     */
    @ApiModelProperty("已结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty("实际已结算金额")
    private BigDecimal realSettleAmount;

    @ApiModelProperty("实际支付金额")
    private BigDecimal realPayAmount;
}
