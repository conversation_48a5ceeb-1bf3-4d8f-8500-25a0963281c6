package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
public class OrderMemberChannelDetailVO implements Serializable {
    private static final long serialVersionUID = -8789634498369809463L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 购买时间
     */
    @ApiModelProperty("购买时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 会员编码
     */
    @ApiModelProperty("会员编码")
    private String memberCode;

    /**
     * 商家id
     */
    @JsonIgnore
    private Long businessId;

    /**
     * 商家名称
     */
    @ApiModelProperty("商家名称")
    private String businessName;

    /**
     * 订单实付金额（单位：￥）
     */
    @ApiModelProperty(value = "订单实付金额（单位：￥）")
    private BigDecimal realPayAmount;

    /**
     * 订单实付金额（对应币种实付）
     */
    @ApiModelProperty("订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;

    /**
     * 币种（详见sys_dict_type.dict_type = sys_money_type）
     */
    @ApiModelProperty("币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    /**
     * 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
     */
    @ApiModelProperty("支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）")
    private Integer payType;

    /**
     * 会员类型
     */
    @ApiModelProperty("会员类型：0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String channelName;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间（当前需求只有一次操作，即运营点击结算的时间）")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date operationTime;

    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty("实际结算金额")
    private BigDecimal realSettleAmount;

    /**
     * 结算人名字
     */
    @ApiModelProperty("结算人名字")
    private String settleUserName;

    /**
     * 结算时间
     */
    @ApiModelProperty("结算时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date settleTime;

    /**
     * 结算凭证地址
     */
    @ApiModelProperty("结算凭证地址")
    private String settleResourceUrl;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 状态
     */
    @ApiModelProperty("状态（0-未结算，1-已结算）")
    private Integer settleStatus;

    @ApiModelProperty("种草码")
    private String seedCode;
}
