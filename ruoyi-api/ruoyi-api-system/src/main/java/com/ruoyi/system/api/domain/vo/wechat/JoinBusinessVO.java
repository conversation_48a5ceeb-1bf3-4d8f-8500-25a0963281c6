package com.ruoyi.system.api.domain.vo.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :加入商家
 * @create :2024-08-02 10:56
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JoinBusinessVO implements Serializable {
    private static final long serialVersionUID = 1078509318036610160L;

    @ApiModelProperty("是否需要添加企业微信：0-否，1-是")
    private Integer needAddWeChat;

    @ApiModelProperty("跳转类型 1:跳转至链接,2:展示二维码")
    Integer type;

    @ApiModelProperty("链接")
    String url;

    @ApiModelProperty("提示信息")
    private String message;

    @ApiModelProperty("状态：0-自定义异常，1-已是该公司子账号，2-已是其它公司子账号，未解绑权限，3-您已是平台主账号，4企业主账号会员已到期，无法加入，5-已申请子账号，无法再次申请，6-有效账号，7-无登录账号，8-用户未授权")
    private Integer status;
}
