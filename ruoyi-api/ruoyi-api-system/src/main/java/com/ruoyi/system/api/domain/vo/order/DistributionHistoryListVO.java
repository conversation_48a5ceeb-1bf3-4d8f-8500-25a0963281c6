package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/17 15:18
 */
@Data
public class DistributionHistoryListVO implements Serializable {
    private static final long serialVersionUID = 7044776936408179219L;

    /**
     * 预选模特ID
     */
    @ApiModelProperty(value = "预选模特ID")
    private Long preselectModelId;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date addTime;

    /**
     * 分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）
     */
    @ApiModelProperty(value = "分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）")
    private Integer distributionResult;

    /**
     * 分发结果原因（1：客服取消，2：订单回退，3：订单暂停匹配，4：模特行程中，5：模特暂停合作，6：模特取消合作，7：未确认，8：交易关闭）
     */
    @ApiModelProperty(value = "分发结果原因（1：客服取消，2：订单回退，3：订单暂停匹配，4：模特行程中，5：模特暂停合作，6：模特取消合作，7：未确认，8：交易关闭）")
    private Integer distributionResultCause;
}
