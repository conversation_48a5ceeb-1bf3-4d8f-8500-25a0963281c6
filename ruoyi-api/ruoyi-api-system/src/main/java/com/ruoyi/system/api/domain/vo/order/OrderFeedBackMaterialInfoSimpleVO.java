package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderFeedBackMaterialInfoSimpleVO implements Serializable {
    private static final long serialVersionUID = 852310476002802045L;

    /**
     * 素材id（FK:order_video_feed_back_material.id）
     */
    @ApiModelProperty(value = "素材id")
    @JsonIgnore
    private Long materialId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;

    /**
     * 上传对象（2:运营,3:模特）
     */
    @ApiModelProperty(value = "上传对象（2:运营,3:模特）")
    private Integer object;

    /**
     * 上传用户id
     */
    @ApiModelProperty(value = "上传用户id")
    @JsonIgnore
    private Long userId;

    /**
     * 上传用户（运营）
     */
    @ApiModelProperty(value = "上传用户（运营）")
    private UserVO back;

    /**
     * 上传用户（模特）
     */
    @ApiModelProperty(value = "上传用户（模特）")
    private ModelInfoVO model;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

}
