package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/3 10:56
 */
@Data
public class OrderModelMyOrderBadgeVO implements Serializable {
    private static final long serialVersionUID = 9110848280827982046L;
    /**
     * 待完成订单数量
     */
    @ApiModelProperty(value = "待完成订单数量")
    private Integer pendingCompletionCount;

    /**
     * 售后订单数量
     */
    @ApiModelProperty(value = "售后订单数量")
    private Integer afterSalesCount;
}
