package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-20
 */
@Data
public class OrderVideoRefundSimpleVO implements Serializable {
    private static final long serialVersionUID = 7302578560932592387L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）", notes = "1:补偿,2:取消订单,3:取消选配")
    private Integer refundType;

    /**
     * 退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）
     */
    @ApiModelProperty(value = "退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）", notes = "0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功")
    private Integer refundStatus;
}
