package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 11:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompanyNotInvoicedOrderDetailVO extends CompanyNotInvoicedListVO {
    private static final long serialVersionUID = -1958969460762361254L;

    /**
     * 关联视频订单
     */
    @ApiModelProperty(value = "关联视频订单")
    private List<OrderVideoSimpleVO> orderVideoSimpleVOS;

    /**
     * 套餐类型：0-季度套餐，1-一年会员，2-三年会员
     */
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer packageType;
}
