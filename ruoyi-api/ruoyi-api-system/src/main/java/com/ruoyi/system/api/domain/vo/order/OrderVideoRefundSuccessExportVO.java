package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-11-7
 */
@Data
public class OrderVideoRefundSuccessExportVO implements Serializable {
    private static final long serialVersionUID = -2198666329445066110L;
    /**
     * 申请时间
     */
    @Excel(name = "退款申请时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19)
    private Date applyTime;

    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 30)
    private String orderNum;

    /**
     * 退款审批号
     */
    @Excel(name = "退款审批号")
    private String refundNum;

    /**
     * 视频编码
     */
    @Excel(name = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @Excel(name = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @Excel(name = "产品英文名")
    private String productEnglish;

    /**
     * 会员编码
     */
    @Excel(name = "会员编码")
    private String merchantCode;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @Excel(name = "退款类型", readConverterExp = "1=补偿,2=取消订单,3=取消选配")
    private Integer refundType;

    /**
     * 发起方（1:商家,2:平台）
     */
    @Excel(name = "发起方", readConverterExp = "1=商家,2=平台")
    private Integer initiatorSource;

    /**
     * 发起人名称
     */
    @Excel(name = "发起人")
    private String initiatorName;

    /**
     * 退款原因
     */
    @Excel(name = "退款原因")
    private String refundCause;

    /**
     * 操作人
     */
    @Excel(name = "审核人")
    private String operateBy;

    /**
     * 操作时间
     */
    @Excel(name = "审核时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN, width = 19)
    private Date operateTime;

    /**
     * 退款金额（单位：￥）
     */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;
}
