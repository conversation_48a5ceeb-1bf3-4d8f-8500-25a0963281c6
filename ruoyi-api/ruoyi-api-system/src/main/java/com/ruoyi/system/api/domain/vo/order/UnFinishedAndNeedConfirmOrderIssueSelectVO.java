package com.ruoyi.system.api.domain.vo.order;

import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/2 10:14
 */
@Data
public class UnFinishedAndNeedConfirmOrderIssueSelectVO {

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long id;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 蜗牛运营用户信息
     */
    @ApiModelProperty(value = "蜗牛运营用户信息")
    private UserVO userVO;
}
