package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 18:20
 */
@Data
public class OrderVideoTaskDetailProcessRecordVO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 记录时间
     */
    @ApiModelProperty(value = "记录时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date time;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    @JsonIgnore
    private Long operateById;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private UserVO operate;

    /**
     * 操作人类型（1：处理人，2：剪辑人，3：系统）
     */
    @ApiModelProperty(value = "操作人类型（1：处理人，2：剪辑人，3：系统）")
    private Integer operateByType;

    /**
     * 操作类型 详见OrderTaskDetailFlowOperateTypeEnum
     * @see com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum
     */
    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    /**
     * 完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）
     */
    @ApiModelProperty(value = "完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）")
    private Integer completionMode;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 对象存储键值（逗号分隔的字符串）
     */
    @ApiModelProperty(value = "对象存储键值（逗号分隔的字符串）")
    @JsonIgnore
    private String objectKeysStr;

    /**
     * 处理记录图片列表
     */
    @ApiModelProperty(value = "处理记录图片列表")
    private List<String> objectKeys;

    @ApiModelProperty(value = "工单编号")
    private String taskNum;
}
