package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/7 11:05
 */
@Data
public class InvoiceFinishListExportVO implements Serializable {
    private static final long serialVersionUID = 2271854220740887234L;

    /**
     * 申票码
     */
    @Excel(name = "申票码")
    private String ticketCode;

    /**
     * 发票号
     */
    @Excel(name = "发票号", defaultValue = StrPool.DASHED)
    private String number;

    /**
     * 包含订单
     */
    @Excel(name = "包含订单")
    private String orderNumStr;

    /**
     * 商家编码
     */
    @Excel(name = "商家编码")
    private String merchantCode;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @Excel(name = "发票类型", readConverterExp = "1=增值税普通发票,2=形式发票")
    private Integer invoiceType;

    /**
     * 开票信息
     */
    @Excel(name = "开票信息")
    private String invoiceInfo;

    /**
     * 开票金额
     */
    @Excel(name = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）
     */
    @Excel(name = "状态", readConverterExp = "0=开票信息待完善,1=待开票,2=待确认,3=已完成,4=已红冲,5=待审核,6=已取消,7=已完成")
    private Integer status;

    /**
     * 操作人
     */
    @Excel(name = "操作人")
    private String operatorUserName;

    /**
     * 操作时间
     */
    @Excel(name = "操作时间", dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date operatorTime;
}
