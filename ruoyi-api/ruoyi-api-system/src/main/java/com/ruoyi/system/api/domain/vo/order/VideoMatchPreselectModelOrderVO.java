package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单_视频_预选模特VO对象
 *
 * <AUTHOR>
 * @date 2024-11-16
 */
@Data
public class VideoMatchPreselectModelOrderVO implements Serializable {
    private static final long serialVersionUID = -4699611782318825418L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long matchId;

    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    @JsonIgnore
    private Long modelId;

    /**
     * 状态（0:未对接,1:已对接,2:已选定,3:已淘汰）
     */
    @ApiModelProperty(value = "状态（0:未对接,1:已对接,2:已选定,3:已淘汰）")
    private Integer status;

    /**
     * 模特
     */
    @ApiModelProperty(value = "模特")
    private ModelOrderSimpleVO model;
}
