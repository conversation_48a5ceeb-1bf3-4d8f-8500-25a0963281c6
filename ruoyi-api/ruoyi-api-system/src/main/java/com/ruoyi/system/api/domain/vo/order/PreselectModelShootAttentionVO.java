package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15 15:29
 */
@Data
public class PreselectModelShootAttentionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("拍摄模特注意事项")
    private String shootAttention;

    @ApiModelProperty("拍摄注意事项对象存储键值")
    private List<String> shootAttentionObjectKey;
}
