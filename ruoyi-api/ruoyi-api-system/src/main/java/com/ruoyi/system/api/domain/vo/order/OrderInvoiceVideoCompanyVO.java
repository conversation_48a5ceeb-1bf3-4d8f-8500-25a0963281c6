package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:07
 */
@Data
public class OrderInvoiceVideoCompanyVO implements Serializable {
    private static final long serialVersionUID = 5403193982993304590L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private List<String> videoCodes;
}
