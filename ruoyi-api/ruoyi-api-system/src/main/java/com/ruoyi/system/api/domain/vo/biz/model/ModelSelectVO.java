package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 模特下拉框返回对象
 *
 * <AUTHOR>
 * @date 2025/8/1 11:03
 */
@Data
public class ModelSelectVO implements Serializable {
    private static final long serialVersionUID = 3077313646347748171L;

    /**
     * 模特ID
     */
    @ApiModelProperty(value = "模特ID")
    private Long id;

    /**
     * 模特账号
     */
    @ApiModelProperty(value = "模特账号")
    private String account;

    /**
     * 模特姓名
     */
    @ApiModelProperty(value = "模特姓名")
    private String name;
}
