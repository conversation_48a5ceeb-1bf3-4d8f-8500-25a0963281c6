package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:07
 */
@Data
public class OrderInvoiceRedOrderVideoVO implements Serializable {
    private static final long serialVersionUID = 2751334958794292914L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票红冲ID (FK:order_invoice_red.id)
     */
    @ApiModelProperty(value = "发票红冲ID")
    private Long invoiceRedId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
     */
    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）")
    private Integer payType;

    /**
     * 视频订单ID (FK:order_video.id)
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）")
    private Integer refundType;

    /**
     * 提现时间
     */
    @ApiModelProperty(value = "提现时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date withdrawDepositTime;

    /**
     * 提现金额
     */
    @ApiModelProperty(value = "提现金额")
    private BigDecimal withdrawDepositAmount;
}
