package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/18 17:16
 */
@Data
public class OrderVideoRoastStatisticsVO implements Serializable {

    private static final long serialVersionUID = -6817731556204745735L;

    @ApiModelProperty(value = "订单吐槽未处理")
    private Integer videoUnHandleCount;

    @ApiModelProperty(value = "订单吐槽已处理")
    private Integer videoHandleCount;

    @ApiModelProperty(value = "订单吐槽总数量")
    private Integer videoCount;

    @ApiModelProperty(value = "系统吐槽已处理")
    private Integer systemHandleCount;

    @ApiModelProperty(value = "系统吐槽未处理")
    private Integer systemUnHandleCount;

    @ApiModelProperty(value = "系统吐槽总数量")
    private Integer systemCount;

}
