package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/1 17:42
 */
@Data
public class ModelLoginVO implements Serializable {
    private static final long serialVersionUID = -47213040312083496L;

    /**
     * 模特ID
     */
    @ApiModelProperty("模特ID")
    private Long id;

    /**
     * 模特账号
     */
    @ApiModelProperty("模特账号")
    private String account;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 模特图片URI
     */
    @ApiModelProperty(value = "模特图片URI")
    private String modelPic;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家")
    private Integer nation;

    /**
     * 模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "模特类型")
    private Integer type;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "平台")
    private String platform;

    /**
     * 是否展示（1:展示,0:不展示）
     */
    @ApiModelProperty(value = "是否展示（1:展示,0:不展示）")
    private Integer isShow;

    /**
     * 合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "合作深度")
    private Integer cooperation;

    /**
     * 模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
     */
    @ApiModelProperty(value = "模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)")
    private Integer status;
}
