package com.ruoyi.system.api.domain.vo.wechat;

import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 手机端页面登录信息
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatChannelLoginVO implements Serializable {

    private static final long serialVersionUID = -3311500606365565229L;
    @ApiModelProperty("登录状态")
    WxChatLoginStatusEnum loginStatus;

    @ApiModelProperty("token")
    String token;
}
