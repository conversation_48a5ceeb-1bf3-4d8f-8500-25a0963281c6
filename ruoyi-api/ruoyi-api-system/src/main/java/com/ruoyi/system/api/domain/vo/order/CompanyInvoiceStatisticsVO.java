package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/11 9:58
 */
@Data
public class CompanyInvoiceStatisticsVO implements Serializable {
    private static final long serialVersionUID = 4614948104222389045L;

    /**
     * 未开票数量
     */
    @ApiModelProperty(value = "未开票数量")
    private Integer unbilledQuantity;

    /**
     * 待开票数量
     */
    @ApiModelProperty(value = "待开票数量")
    private Integer quantityToBeInvoiced;

    /**
     * 已开票数量
     */
    @ApiModelProperty(value = "已开票数量")
    private Integer invoicedQuantity;
}
