package com.ruoyi.system.api.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsVo implements Serializable {
    private static final long serialVersionUID = -1896903809057422424L;
    @ApiModelProperty("手机号")
    private String phoneNum;
    @ApiModelProperty("uuid")
    private String uuid;
    @ApiModelProperty("验证码")
    private String code;
    @ApiModelProperty("ticket")
    private String ticket;
    @ApiModelProperty("验证码")
    private String verifyToken;
}
