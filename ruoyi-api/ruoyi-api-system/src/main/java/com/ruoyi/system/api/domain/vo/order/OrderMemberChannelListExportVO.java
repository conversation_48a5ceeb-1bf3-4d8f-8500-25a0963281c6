package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-01-21 11:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberChannelListExportVO implements Serializable {
    private static final long serialVersionUID = 6547151477528825531L;
    private Long id;

    /**
     * 渠道ID、渠道名称、商家名称、商家微信名、会员编码、会员类型、购买时间、结算比例、结算金额、状态*
     */
    @Excel(name = "渠道ID")
    private Long channelId;

    @Excel(name = "渠道名称")
    private String channelName;

    private String channelPhone;

    @JsonIgnore
    private Long businessId;

    @Excel(name = "商家名称", defaultValue = "-")
    private String businessName;

    @Excel(name = "商家微信名")
    private String bizUserNickName;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "会员类型", readConverterExp = "0=季度会员,1=年度会员,2=三年会员")
    private Integer memberPackageType;

    @Excel(name = "购买时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "结算方案",readConverterExp= "1=固定金额,2=固定比例")
    private Integer settleType;

    @Excel(name = "结算比例/金额")
    private String settleRageFormatted;

    @Excel(name = "结算金额")
    private BigDecimal settleAmount;

    @Excel(name = "实际结算金额", defaultValue = StrPool.DASHED)
    private String realSettleAmountFormatted;

//    @Excel(name = "结算时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
//     @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
//     private Date settleTime;

    @Excel(name = "状态", readConverterExp = "0=待入账,1=可提现,2=提现审核中,3=待打款,4=已打款,5=审核不通过,6=打款异常,99=入账失败")
    private Integer settleStatus;

    @Excel(name = "打款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", defaultValue = "-")
    private Date settleTime;


    private String bizUserPhone;

    private Long bizUserId;
}
