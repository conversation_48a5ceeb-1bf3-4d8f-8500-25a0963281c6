package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-20
 */
@Data
public class OrderVideoRefundSuccessVO implements Serializable {
    private static final long serialVersionUID = -2198666329445066110L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 退款审批号
     */
    @ApiModelProperty(value = "退款审批号")
    private String refundNum;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 会员编码
     */
    @ApiModelProperty(value = "会员编码")
    private String merchantCode;

    /**
     * 退款类型（1:补偿,2:取消订单,3:取消选配）
     */
    @ApiModelProperty(value = "退款类型（1:补偿,2:取消订单,3:取消选配）", notes = "1:补偿,2:取消订单,3:取消选配")
    private Integer refundType;

    /**
     * 发起方（1:商家,2:平台）
     */
    @ApiModelProperty(value = "发起方（1:商家,2:平台）", notes = "1:商家,2:平台")
    private Integer initiatorSource;

    /**
     * 发起人名称
     */
    @ApiModelProperty(value = "发起人名称")
    private String initiatorName;

    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    /**
     * 退款原因
     */
    @ApiModelProperty(value = "退款原因")
    private String refundCause;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operateBy;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    /**
     * 退款金额（单位：￥）
     */
    @ApiModelProperty(value = "退款金额（单位：￥）", notes = "单位：￥")
    private BigDecimal refundAmount;
}
