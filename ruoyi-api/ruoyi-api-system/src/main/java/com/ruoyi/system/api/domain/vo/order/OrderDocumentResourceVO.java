package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
public class OrderDocumentResourceVO implements Serializable {
    private static final long serialVersionUID = 5919817538555587197L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 图片资源URI
     */
    @ApiModelProperty(value = "图片资源URI")
    private String objectKey;

    /**
     * 0-商家上传， 1-平台上传
     */
    @ApiModelProperty(value = "0-商家上传， 1-平台上传")
    private Integer uploadType;
}
