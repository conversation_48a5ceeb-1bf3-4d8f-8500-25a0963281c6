package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2025/2/24 11:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelHomePageOrderStatisticsVO implements Serializable {
    private static final long serialVersionUID = -2598045101237711024L;

    /**
     * 给我的订单数量
     */
    @ApiModelProperty(value = "给我的订单数量")
    private Long forMeCount;

    /**
     * 可接单页订单数量
     */
    @ApiModelProperty(value = "给我的订单数量")
    private Long allCount;

    /**
     * RECOMMEND页面-数量统计
     */
    @ApiModelProperty(value = "RECOMMEND页面-数量统计")
    private Long recommendCount;
}
