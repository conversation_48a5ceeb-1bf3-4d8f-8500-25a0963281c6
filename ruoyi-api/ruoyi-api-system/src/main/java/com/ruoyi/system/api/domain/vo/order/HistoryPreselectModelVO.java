package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.model.AddPreselectModelListVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单_视频_预选模特VO对象
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
public class HistoryPreselectModelVO implements Serializable {
    private static final long serialVersionUID = -3183325061268021576L;
    /**
     * 匹配单ID
     */
    @ApiModelProperty(value = "匹配单ID")
    private Long id;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "排单类型（1:排单,2:携带排单）")
    private Integer scheduleType;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "超额说明")
    private String overstatement;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 主携带数量
     */
    @ApiModelProperty(value = "主携带数量")
    private Integer mainCarryCount;

    /**
     * 主携带视频订单id (FK:order_video.id)
     */
    @ApiModelProperty(value = "主携带视频订单id (FK:order_video.id)")
    private Long mainCarryVideoId;

    /**
     * 发货备注
     */
    @ApiModelProperty(value = "发货备注")
    private String shippingRemark;

    /**
     * 发货图片(关联资源id)
     */
    @ApiModelProperty(value = "发货图片(关联资源id)")
    @JsonIgnore
    private String shippingPic;

    /**
     * 发货图片
     */
    @ApiModelProperty(value = "发货图片")
    private List<String> shippingPics = new ArrayList<>();

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private AddPreselectModelListVO shootModel;

    /**
     * 拍摄模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "拍摄模特类型(0:影响者,1:素人)")
    private Integer shootModelType;

    /**
     * 拍摄模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "拍摄模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private String shootModelPlatform;

    /**
     * 拍摄模特合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "拍摄模特合作深度(0:一般模特,1:优质模特,2:中度模特)")
    private Integer shootModelCooperation;

    /**
     * 拍摄模特对接人ID
     */
    @ApiModelProperty(value = "拍摄模特对接人ID")
    private Long shootModelPersonId;

    /**
     * 拍摄模特对接人名称
     */
    @ApiModelProperty(value = "拍摄模特对接人名称")
    private String shootModelPersonName;

    /**
     * 拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加）
     */
    @ApiModelProperty(value = "拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加）")
    private Integer shootModelAddType;

    /**
     * 预选模特
     */
    @ApiModelProperty(value = "预选模特")
    private List<OrderVideoMatchPreselectModelVO> preselectModelList = new ArrayList<>();
}
