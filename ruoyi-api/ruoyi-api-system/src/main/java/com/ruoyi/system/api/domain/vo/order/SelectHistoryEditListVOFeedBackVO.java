package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:37
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectHistoryEditListVOFeedBackVO implements Serializable {
    private static final long serialVersionUID = 2085421634202221891L;

    /**
     * 反馈人姓名
     */
    @ApiModelProperty(value = "反馈人姓名")
    private String feedbackBy;

    /**
     * 反馈人ID
     */
    @ApiModelProperty(value = "反馈人ID")
    @JsonIgnore
    private Long feedbackById;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date feedbackTime;

    /**
     * 反馈链接类型
     */
    @ApiModelProperty(value = "反馈类型（1:视频,2:视频和照片,3:照片）")
    private Integer type;

    /**
     * 视频地址
     */
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    private String picUrl;
}
