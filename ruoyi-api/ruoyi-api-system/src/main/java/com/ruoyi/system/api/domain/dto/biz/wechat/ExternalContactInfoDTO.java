package com.ruoyi.system.api.domain.dto.biz.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalContactInfoDTO implements Serializable {


    private static final long serialVersionUID = 1132522300692196878L;
    /**
     * 外部联系人id
     */
    private String externalUserid;
    /**
     * 用户名
     */
    private String name;
    /**
     * 职位
     */
    private String position;
    /**
     * 头像url
     */
    private String avatar;
    /**
     * 公司名称
     */
    private String corpName;
    /**
     * 公司全名称
     */
    private String corpFullName;
    /**
     * 该成员添加此外部联系人所打标签类型, 1-企业设置，2-用户自定义，3-规则组标签
     */
    private Integer type;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * unionId
     */
    private String unionid;

    /**
     * 渠道信息
     */
    private String channel;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 渠道Id
     */
    private Long channelId;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 企业微信外部人员id
     */
    private String connectUserId;

    /**
     * 企业微信外部人员姓名
     */
    private String connectUserName;

}
