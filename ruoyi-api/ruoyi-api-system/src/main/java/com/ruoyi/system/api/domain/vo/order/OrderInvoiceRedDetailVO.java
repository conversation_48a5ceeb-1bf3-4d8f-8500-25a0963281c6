package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8 11:03
 */
@Data
public class OrderInvoiceRedDetailVO implements Serializable {
    private static final long serialVersionUID = 8430037917296971072L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票详情
     */
    @ApiModelProperty(value = "发票详情")
    private OrderInvoiceDetailVO orderInvoiceDetailVO;

    /**
     * 红冲状态（1：待红冲，2：不红冲，3：已红冲）
     */
    @ApiModelProperty(value = "红冲状态（1：待红冲，2：不红冲，3：已红冲）")
    private Integer invoiceRedStatus;

    /**
     * 红冲原因（1：重开发票，2：商家提现）
     */
    @ApiModelProperty(value = "红冲原因（1：重开发票，2：商家提现）")
    private Integer invoiceRedCause;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date applyTime;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    private Integer invoiceType;

    /**
     * 抬头类型（1：企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位）")
    private Integer titleType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 发票备注
     */
    @ApiModelProperty(value = "发票备注")
    private String invoiceRemark;

    /**
     * 红冲关联订单
     */
    @ApiModelProperty(value = "红冲关联订单")
    private List<OrderInvoiceRedOrderVideoVO> orderInvoiceRedOrderVideoVOS;
}
