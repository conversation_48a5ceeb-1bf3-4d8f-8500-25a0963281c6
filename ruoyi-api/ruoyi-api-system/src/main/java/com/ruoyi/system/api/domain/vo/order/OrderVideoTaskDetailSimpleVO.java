package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25 18:11
 */
@Data
public class OrderVideoTaskDetailSimpleVO implements Serializable {
    private static final long serialVersionUID = -6664390988743699894L;

    /**
     * 反馈给商家ID
     */
    @ApiModelProperty(value = "反馈给商家ID")
    private Long feedBackId;

    /**
     * 任务详情ID
     */
    @ApiModelProperty(value = "任务详情ID")
    private Long taskDetailId;

    /**
     * 售后分类（1：视频，2：照片）
     */
    @ApiModelProperty(value = "售后分类（1：视频，2：照片）")
    private Integer afterSaleClass;

    /**
     * 售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）
     */
    @ApiModelProperty(value = "售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）")
    private Integer afterSaleVideoType;

    /**
     * 售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）
     */
    @ApiModelProperty(value = "售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）")
    private Integer afterSalePicType;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private Integer workOrderType;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;
}
