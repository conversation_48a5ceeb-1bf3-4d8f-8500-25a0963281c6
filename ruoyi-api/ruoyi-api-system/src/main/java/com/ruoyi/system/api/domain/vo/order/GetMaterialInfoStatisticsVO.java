package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/20 17:21
 */
@Data
public class GetMaterialInfoStatisticsVO implements Serializable {
    private static final long serialVersionUID = -3067772159728812745L;

    /**
     * 总反馈数量
     */
    @ApiModelProperty(value = "总反馈数量")
    private Integer totalFeedbackCount;

    /**
     * 今日反馈数量
     */
    @ApiModelProperty(value = "今日反馈数量")
    private Integer todayFeedbackCount;

    /**
     * 待领取数量
     */
    @ApiModelProperty(value = "待领取数量")
    private Integer getCount;

    /**
     * 待下载数量
     */
    @ApiModelProperty(value = "待下载数量")
    private Integer downloadCount;

    /**
     * 今日已下载数量
     */
    @ApiModelProperty(value = "今日已下载数量")
    private Integer todayDownloadCount;
}
