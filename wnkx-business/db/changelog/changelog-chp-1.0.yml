databaseChangeLog:
  - logicalFilePath: 'changelog-chp-1.0.yml'
  - changeSet:
      id: chp-1
      author: chp
      changes:
        - createTable:
            tableName: business_account_apply
            remarks: 账号申请表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: owner_account
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 主账号
              - column:
                  name: name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 员工名称
              - column:
                  name: nick_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 微信昵称
              - column:
                  name: pic
                  type: varchar(500)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 头像
              - column:
                  name: unionid
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: unionid
              - column:
                  name: external_user_id
                  type: varchar(32)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 企业微信外部联系人id
              - column:
                  name: audit_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 审核状态（0:待审核,1:审核通过,2.拒绝）
              - column:
                  name: audit_time
                  type: datetime
                  remarks: 审核时间
              - column:
                  name: creat_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
  - changeSet:
      id: chp-2
      author: chp
      changes:
        - createIndex:
            tableName: business_account_apply
            indexName: idx_unionid
            unique: false
            columns:
              - column:
                  name: unionid
  - changeSet:
      id: chp-3
      author: chp
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: use_balance
                  afterColumn: balance
                  type: decimal(12,2)
                  defaultValueComputed: 0.00
                  constraints:
                    nullable: false
                  remarks: 使用余额

  - changeSet:
      id: chp-4
      author: chp
      changes:
        - createTable:
            tableName: business_balance_audit_flow
            remarks: 余额提现审核表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID
              - column:
                  name: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueComputed: 0.00
                  remarks: 提现金额
              - column:
                  name: real_amount
                  type: decimal(12,2)
                  remarks: 实付金额（单位：￥）
              - column:
                  name: pay_time
                  type: datetime
                  remarks: 支付时间
              - column:
                  name: resource_id
                  type: bigint(20)
                  remarks: 图片资源地址id（FK：resource.id）
              - column:
                  name: audit_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 审核状态（0:待处理,1:已提现,2.已取消）
              - column:
                  name: audit_user_id
                  type: bigint(20)
                  remarks: 审核人员id FK sys_user.user_id
              - column:
                  name: audit_time
                  type: datetime
                  remarks: 审核时间
              - column:
                  name: remark
                  type: varchar(300)
                  remarks: 备注
              - column:
                  name: create_user_id
                  type: bigint(20)
                  remarks: 创建人id FK sys_user.user_id
              - column:
                  name: creat_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
  - changeSet:
      id: chp-5
      author: chp
      changes:
        - renameColumn:
            tableName: business_account_apply
            oldColumnName: creat_time
            newColumnName: create_time
            columnDataType: datetime

        - renameColumn:
            tableName: business_balance_audit_flow
            oldColumnName: creat_time
            newColumnName: create_time
            columnDataType: datetime
  - changeSet:
      id: chp-6
      author: chp
      changes:
        - modifyDataType:
            tableName: business_balance_audit_flow
            columnName: resource_id
            newDataType: varchar(64)
        - renameColumn:
            tableName: business_balance_audit_flow
            oldColumnName: resource_id
            newColumnName: resource_url
            columnDataType: varchar(64)

  - changeSet:
      id: chp-7
      author: chp
      changes:
        - createTable:
            tableName: page_config
            remarks: 页面配置表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 页面名称
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 1-首页，2-精选案例，3-其他
                  defaultValueNumeric: 3
              - column:
                  name: link
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 链接地址
                  defaultValue: ''
              - column:
                  name: content
                  type: json
                  constraints:
                    nullable: false
                  remarks: 页面配置
              - column:
                  name: platform
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 平台类型：0:Amazon,1:tiktok,2:其他
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
        - sql:
            sql: |
              INSERT INTO `page_config`(`id`, `name`, `type`, `content`, `platform`) VALUES (1, '首页', 1, '{}', 2);
              INSERT INTO `page_config`(`id`, `name`, `type`, `content`, `platform`) VALUES (2, 'TikTok-精选案例页', 2, '{}', 1 );
              INSERT INTO `page_config`(`id`, `name`, `type`, `content`, `platform`) VALUES (3, 'Amazon-精选案例页', 2, '{}', 0);


  - changeSet:
      id: chp-8
      author: chp
      changes:
        - createTable:
            tableName: biz_user
            remarks: 登录用户表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 员工名称
                  defaultValue: ''
              - column:
                  name: nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 微信昵称
              - column:
                  name: pic
                  type: varchar(500)
                  constraints:
                    nullable: false
                  remarks: 头像
                  defaultValue: ''
              - column:
                  name: unionid
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: unionId
                  defaultValue: ''
              - column:
                  name: phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 手机号
                  defaultValue: ''
              - column:
                  name: external_user_id
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: ExternalUserID企业微信外部联系人id
                  defaultValue: ''
              - column:
                  name: is_proxy
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否为代理(0:否,1:是)
                  defaultValueNumeric: 0
              - column:
                  name: customer_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 客户类型 （2-普通客户 0-一般客户 1-重要客户）
                  defaultValueNumeric: 0
              - column:
                  name: waiter_id
                  type: bigint(20)
                  remarks: 对接客服  FK：sys_user.user_id
              - column:
                  name: business_name
                  type: varchar(20)
                  remarks: 商家名称
                  defaultValue: ''
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 账号状态（0正常 1禁用 2被删除）
                  defaultValueNumeric: 0
              - column:
                  name: last_login_time
                  type: datetime
                  remarks: 最后登录时间
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
              - column:
                  name: is_del
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否删除（0-未删除，1-已删除）
                  defaultValueNumeric: 0

        - createIndex:
            tableName: biz_user
            indexName: uk_phone
            unique: true
            columns:
              - column:
                  name: phone
        - createIndex:
            tableName: biz_user
            indexName: uk_unionid
            unique: true
            columns:
              - column:
                  name: unionid
        - addColumn:
            tableName: business_account
            columns:
              - column:
                  name: biz_user_id
                  afterColumn: business_id
                  type: bigint(16)
                  remarks: 登录用户ID
        - addColumn:
            tableName: business_account
            columns:
              - column:
                  name: is_owner_account
                  afterColumn: biz_user_id
                  type: tinyint(1)
                  remarks: 是否主账号（0-否， 1-是）
  - changeSet:
      id: chp-9
      author: chp
      changes:
        - addColumn:
            tableName: biz_user
            columns:
              - column:
                  name: account_type
                  afterColumn: customer_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 账号状态：0-普通账号，1-主账号，2-子账号
                  defaultValueNumeric: 0
        - dropIndex:
            tableName: biz_user
            indexName: uk_phone
  - changeSet:
      id: chp-10
      author: chp
      changes:
        - addDefaultValue:
            tableName: biz_user
            columnName: customer_type
            columnDataType: tinyint(1)
            defaultValueNumeric: 2
  - changeSet:
      id: chp-11
      author: chp
      changes:
        - dropIndex:
            tableName: business_account
            indexName: uk_unionid
        - dropIndex:
            tableName: business_account
            indexName: uk_external_user_id
  - changeSet:
      id: chp-12
      author: chp
      changes:
        - addColumn:
            tableName: business_account_apply
            columns:
              - column:
                  name: biz_user_id
                  afterColumn: owner_account
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 登录账号ID biz_user.id
        - addColumn:
            tableName: business_account_apply
            columns:
              - column:
                  name: business_id
                  afterColumn: biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id business.id
  - changeSet:
      id: chp-13
      author: chp
      changes:
        - setColumnRemarks:
            tableName: business_balance_flow
            columnName: create_user_id
            columnDataType: bigint
            remarks: 创建人id(FK:business_account.id、sys_user.user_id)
        - setColumnRemarks:
            tableName: business_balance_flow
            columnName: update_user_id
            columnDataType: bigint
            remarks: 修改人id(FK:business_account.id、sys_user.user_id)
        - setColumnRemarks:
            tableName: business_balance_flow
            columnName: update_user_name
            columnDataType: varchar(30)
            remarks: 创建人名称(FK:biz_user.nick_name、sys_user.user_name)
        - setColumnRemarks:
            tableName: business_balance_flow
            columnName: create_user_name
            columnDataType: varchar(30)
            remarks: 修改人名称(FK:biz_user.nick_name、sys_user.user_name)
        - setColumnRemarks:
            tableName: business_balance_flow
            columnName: origin
            columnDataType: tinyint(1)
            remarks: 订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出、6.提现支出)

  - changeSet:
      id: chp-14
      author: chp
      changes:
        - dropColumn:
            tableName: business_account
            columnName: nick_name
        - dropColumn:
            tableName: business_account
            columnName: pic
        - dropColumn:
            tableName: business_account
            columnName: unionid
        - dropColumn:
            tableName: business_account
            columnName: external_user_id
  - changeSet:
      id: chp-15
      author: chp
      changes:
        - createTable:
            tableName: business_oper_log
            isNotExists: true
            remarks: 商家信息操作日志
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 订单id (FK:sys_user.user_id)
              - column:
                  name: user_name
                  type: varchar(30)
                  remarks: 用户账号
              - column:
                  name: nick_name
                  type: varchar(30)
                  remarks: 用户昵称
              - column:
                  name: original_business
                  type: json
                  constraints:
                    nullable: false
                  remarks: 原视频json
              - column:
                  name: result_business
                  type: json
                  constraints:
                    nullable: false
                  remarks: 修改后视频json
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: chp-16
      author: chp
      changes:
        - createTable:
            tableName: distribution_channel
            isNotExists: true
            remarks: 分销渠道信息表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 用户ID (FK:biz_user.id)
              - column:
                  name: channel_name
                  type: varchar(27)
                  remarks: 渠道名称
              - column:
                  name: user_name
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 负责人姓名
              - column:
                  name: broke_rage
                  type: decimal(12,1)
                  constraints:
                    nullable: false
                  remarks: 佣金比例
                  defaultValue: 0.0
              - column:
                  name: seed_code
                  type: varchar(4)
                  constraints:
                    nullable: false
                  remarks: 种草码
              - column:
                  name: dedicated_link_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 专属链接code（FX + 8位随机字符）
              - column:
                  name: we_chat_url
                  type: varchar(150)
                  constraints:
                    nullable: false
                  remarks: 专属企微二维码地址
              - column:
                  name: tag_id
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 标签id
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 分销状态（0=正常,1=禁用）
              - column:
                  name: remark
                  type: varchar(60)
                  constraints:
                    nullable: false
                  remarks: 备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 创建人
        - createTable:
            tableName: marketing_channel
            isNotExists: true
            remarks: 市场渠道信息表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: marketing_platform
                  type: tinyint(2)
                  constraints:
                    nullable: false
                  remarks: 市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)
              - column:
                  name: marketing_channel_name
                  type: varchar(27)
                  constraints:
                    nullable: false
                  remarks: 市场渠道名称
              - column:
                  name: dedicated_link_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 专属链接code（SC + 8位随机字符）
              - column:
                  name: we_chat_url
                  type: varchar(150)
                  constraints:
                    nullable: false
                  remarks: 专属企微二维码地址
              - column:
                  name: tag_id
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 标签id
              - column:
                  name: unique_visitor
                  type: int(32)
                  constraints:
                    nullable: false
                  remarks: 独立访客
                  defaultValueNumeric: 0
              - column:
                  name: page_view
                  type: int(32)
                  constraints:
                    nullable: false
                  remarks: 访问量
                  defaultValueNumeric: 0
              - column:
                  name: bounce_rate
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 跳出率
                  defaultValueComputed: 0.00
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 状态（0=正常,1=禁用）
              - column:
                  name: remark
                  type: varchar(60)
                  constraints:
                    nullable: false
                  remarks: 备注
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 创建人
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP

        - createTable:
            tableName: biz_user_channel
            isNotExists: true
            remarks: 用户渠道信息表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 用户ID (FK:biz_user.id)
              - column:
                  name: register_channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 注册渠道类型(0=普通,1=分销，2=市场)
                  defaultValueNumeric: 0
              - column:
                  name: register_channel_id
                  type: bigint(20)
                  remarks: 注册渠道账户id
              - column:
                  name: register_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 注册时间
              - column:
                  name: wechat_channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 企微渠道类型(0=普通,1=分销，2=市场)
                  defaultValueNumeric: 0
              - column:
                  name: wechat_channel_id
                  type: bigint(20)
                  remarks: 添加企微渠道账户id
              - column:
                  name: add_wechat_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 添加企微时间

        - createIndex:
            tableName: biz_user_channel
            indexName: uk_biz_user_id
            unique: true
            columns:
              - column:
                  name: biz_user_id

        - createIndex:
            tableName: distribution_channel
            indexName: uk_biz_user_id
            unique: true
            columns:
              - column:
                  name: biz_user_id

        - addColumn:
            tableName: biz_user
            columns:
              - column:
                  name: channel_status
                  afterColumn: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 渠道状态（0=正常, 1=禁用）
                  defaultValueNumeric: 1

        - addColumn:
            tableName: business
            columns:
              - column:
                  name: seed_code
                  afterColumn: member_code
                  type: varchar(4)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 种草码（distribution_channel.seed_code）

        - addColumn:
            tableName: we_chat_external_user
            columns:
              - column:
                  name: channel
                  afterColumn: status
                  type: varchar(64)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 渠道信息

  - changeSet:
      id: chp-17
      author: chp
      changes:
        - addColumn:
            tableName: biz_user_channel
            columns:
              - column:
                  name: is_activate
                  afterColumn: add_wechat_time
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 1
                  remarks: 是否激活（0=否,1=是）
  - changeSet:
      id: chp-18
      author: chp
      changes:
        - addColumn:
            tableName: marketing_channel
            columns:
              - column:
                  name: create_id
                  afterColumn: create_by
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建人id
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: create_id
                  afterColumn: create_by
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建人id
  - changeSet:
      id: chp-19
      author: chp
      changes:
        - modifyDataType:
            tableName: distribution_channel
            columnName: remark
            newDataType: varchar(64)
        - modifyDataType:
            tableName: marketing_channel
            columnName: remark
            newDataType: varchar(64)

  - changeSet:
      id: chp-20
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 修改时间
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                  remarks: 修改人名称
              - column:
                  name: update_id
                  type: bigint(20)
                  constraints:
                  remarks: 修改人id
  - changeSet:
      id: chp-21
      author: chp
      changes:
        - dropColumn:
            tableName: biz_user
            columnName: channel_status
        - dropIndex:
            tableName: biz_user
            indexName: uk_unionid
        - addDefaultValue:
            tableName: biz_user
            columnName: nick_name
            columnDataType: varchar(32)
            defaultValue: ''
  - changeSet:
      id: chp-22
      author: chp
      changes:
        - addDefaultValue:
            tableName: biz_user_channel
            columnName: is_activate
            columnDataType: tinyint(1)
            defaultValueNumeric: 0
        - renameColumn:
            tableName: biz_user_channel
            oldColumnName: wechat_channel_type
            columnDataType: tinyint(1)
            newColumnName: wechat_channel_type
            remarks: 企微渠道类型(0=普通,1=市场，2=分销)
        - renameColumn:
            tableName: biz_user_channel
            oldColumnName: register_channel_type
            columnDataType: tinyint(1)
            newColumnName: register_channel_type
            remarks: 注册渠道类型(0=普通,1=市场，2=分销)
        - renameColumn:
            tableName: biz_user_channel
            oldColumnName: wechat_channel_type
            columnDataType: tinyint(1)
            newColumnName: wechat_channel_type
            remarks: 企微渠道类型(0=普通,1=市场，2=分销)
  - changeSet:
      id: chp-23
      author: chp
      changes:
        - createTable:
            tableName: order_member_channel
            isNotExists: true
            remarks: 会员渠道记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id（distribution_channel.id）
              - column:
                  name: channel_name
                  type: varchar(27)
                  constraints:
                    nullable: false
                  remarks: 渠道名称（distribution_channel.channel_name）
              - column:
                  name: channel_phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 渠道手机号（biz_user.phone）
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: business_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 商家名称（business.name）
              - column:
                  name: biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户id FK:biz_user.id
              - column:
                  name: biz_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户微信昵称
              - column:
                  name: biz_user_phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户手机号
              - column:
                  name: member_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 会员编码（business.member_code）
              - column:
                  name: member_package_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 套餐类型：0=季度会员,1=年度会员,2=三年会员
              - column:
                  name: real_pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单实付金额（单位：￥）
              - column:
                  name: settle_rage
                  type: decimal(12,1)
                  constraints:
                    nullable: false
                  remarks: 结算比例
                  defaultValue: 0.0
              - column:
                  name: settle_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 最终结算金额（结算比例 * 会员价格）
                  defaultValue: 0.00
              - column:
                  name: settle_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 结算时间
              - column:
                  name: settle_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 状态（0-未结算，1-已结算）
                  defaultValueNumeric: 0
              - column:
                  name: settle_resource_url
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 结算凭证地址
                  defaultValue: ''
              - column:
                  name: settle_user_name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 结算人名字（sys_user.user_name）
                  defaultValue: ''
              - column:
                  name: settle_user_id
                  type: bigint(20)
                  constraints:
                  remarks: 结算人id（sys_user.user_id）
              - column:
                  name: remark
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 备注
                  defaultValue: ''
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 购买时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: operation_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 操作时间（当前需求只有一次操作，即运营点击结算的时间）
  - changeSet:
      id: chp-24
      author: chp
      changes:
        - addDefaultValue:
            tableName: biz_user_channel
            columnName: register_channel_type
            columnDataType: tinyint(1)
            remarks: 注册渠道类型(0=普通,1=市场，2=分销)
            defaultValueNumeric: 0
        - addDefaultValue:
            tableName: biz_user_channel
            columnName: wechat_channel_type
            columnDataType: tinyint(1)
            remarks: 企微渠道类型(0=普通,1=市场，2=分销)
            defaultValueNumeric: 0
  - changeSet:
      id: chp-25
      author: chp
      changes:
        - createTable:
            tableName: we_chat_tag
            isNotExists: true
            remarks: 企业微信标签信息表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: group_id
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 标签组ID
              - column:
                  name: group_name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 标签组名称
              - column:
                  name: tag_id
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 标签id
              - column:
                  name: tag_name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 标签名称
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP

  - changeSet:
      id: chp-26
      author: chp
      changes:
        - createIndex:
            tableName: we_chat_tag
            indexName: uk_tag_id
            unique: true
            columns:
              - column:
                  name: tag_id

  - changeSet:
      id: chp-27
      author: chp
      changes:
        - renameColumn:
            tableName: model
            oldColumnName: cooperation
            columnDataType: tinyint(1)
            newColumnName: cooperation
            remarks: 合作深度(0:一般模特,1:优质模特,2:中等模特)
  - changeSet:
      id: chp-28
      author: chp
      changes:
        - createTable:
            tableName: business_member_validity_flow
            isNotExists: true
            remarks: 商家会员有效期修改流水
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID（business_id）
              - column:
                  name: origin_member_validity
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 原会员有效期
              - column:
                  name: result_member_validity
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 修改后会员有效期
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 修改原因
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 处理人（sys_user.user_id）
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 处理人名称（sys_user.user_name）
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 处理时间
                  defaultValueComputed: CURRENT_TIMESTAMP

  - changeSet:
      id: chp-29
      author: chp
      changes:
        - createTable:
            tableName: business_balance_prepay
            remarks: 商家预付表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID
              - column:
                  name: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  defaultValueComputed: 0.00
                  remarks: 申请增加金额（单位：￥）
              - column:
                  name: real_amount
                  type: decimal(12,2)
                  remarks: 实际增加金额（单位：￥）
              - column:
                  name: pay_time
                  type: datetime
                  remarks: 支付时间
              - column:
                  name: resource_id
                  type: varchar(300)
                  remarks: 图片资源地址id（FK：resource.id）使用，隔开
              - column:
                  name: audit_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 审核状态（0:待处理,1:审核通过,2.审核拒绝）
              - column:
                  name: audit_time
                  type: datetime
                  remarks: 审核时间
              - column:
                  name: audit_user_id
                  type: bigint(20)
                  remarks: 审核人员id FK sys_user.user_id
              - column:
                  name: audit_user_name
                  type: varchar(30)
                  remarks: 审核人员名称
              - column:
                  name: reject_cause
                  type: varchar(100)
                  remarks: 拒绝原因
              - column:
                  name: remark
                  type: varchar(300)
                  remarks: 备注
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建人id（sys_user.user_id）
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 创建人名称（sys_user.user_name）
              - column:
                  name: creat_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间


  - changeSet:
      id: chp-30
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: prepay_num
                  afterColumn: business_id
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 预付单号
        - createIndex:
            tableName: business_balance_prepay
            indexName: uk_prepay_num
            unique: true
            columns:
              - column:
                  name: prepay_num


  - changeSet:
      id: chp-31
      author: chp
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: member_package_type
                  afterColumn: result_member_validity
                  type: tinyint(1)
                  remarks: 套餐类型：0-季度套餐，1-一年会员，2-三年会员
              - column:
                  name: type
                  afterColumn: result_member_validity
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 处理类型：0-系统调整，1-商家购买


  - changeSet:
      id: chp-32
      author: chp
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: scale
                  afterColumn: name
                  type: tinyint(1)
                  remarks: 企业规模：1=0-20人,2=21-100人,3=101-200人,4=201-1000人,5=超过1000人

  - changeSet:
      id: chp-33
      author: chp
      changes:
        - modifyDataType:
            tableName: business
            columnName: name
            newDataType: varchar(50)
  - changeSet:
      id: chp-34
      author: chp
      changes:
        - modifyDataType:
            tableName: order_member_channel
            columnName: business_name
            newDataType: varchar(50)
        - dropColumn:
            tableName: biz_user
            columnName: business_name

  - changeSet:
      id: chp-order-1731899034
      author: chp
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: presented_time
                  type: int(8)
                  afterColumn: result_member_validity
                  remarks: 加赠时间（单位：月）
                  defaultValue: 0
  - changeSet:
      id: chp-1732065262
      author: chp
      changes:
        - addColumn:
            tableName: text_table
            columns:
              - column:
                  name: status
                  afterColumn: content
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 0启用，1-禁用
              - column:
                  name: sort
                  afterColumn: content
                  type: int(6)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 排序值
              - column:
                  name: type
                  afterColumn: content
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 文本类型：0-协议信息,1-帮助中心-常见问题,2-帮助中心-新手指南

  - changeSet:
      id: chp-1732088659
      author: chp
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: order_num
                  afterColumn: business_id
                  type: varchar(30)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 订单号

  - changeSet:
      id: chp-1732262193
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `text_table`
              MODIFY COLUMN `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文本名称' AFTER `id`;
  - changeSet:
      id: chp-1732265476
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `text_table_history`
              MODIFY COLUMN `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文本名称' AFTER `text_id`;

  - changeSet:
      id: chp-1732267417
      author: chp
      changes:
        - addColumn:
            tableName: page_config
            columns:
              - column:
                  name: user_perms
                  afterColumn: platform
                  type: varchar(30)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 权限标识
        - sql:
            sql: |
              update page_config
              set user_perms = "page_config_main" where name = "首页" and id = 1;
              update page_config
              set user_perms = "page_config_amazon" where name = "TikTok-精选案例页" and id = 2;
              update page_config
              set user_perms = "page_config_tiktok" where name = "Amazon-精选案例页" and id = 3;
  - changeSet:
      id: chp-1732677798
      author: chp
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: join_family_time
                  afterColumn: model_pic
                  type: datetime
                  remarks: 加入家庭时间
              - column:
                  name: model_family_relationship
                  afterColumn: model_pic
                  type: tinyint(1)
                  remarks: 亲属关系(0=发起人,1=母子,2=母女,3=夫妻,4=父子,5=父女,6=兄弟,7=姐妹,8=兄妹,9=姐弟)
              - column:
                  name: family_id
                  afterColumn: model_pic
                  type: bigint(20)
                  remarks: 家庭id
              - column:
                  name: is_initiator
                  afterColumn: model_pic
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 是否家庭模特发起者：0-否,1-是
              - column:
                  name: is_family_model
                  afterColumn: model_pic
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 是否家庭模特：0-否,1-是

  - changeSet:
      id: chp-1732864760
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: apply_remark
                  afterColumn: audit_user_name
                  type: varchar(100)
                  remarks: 申请备注
              - column:
                  name: pay_amount
                  afterColumn: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 应付金额（单位：￥）
              - column:
                  name: tax_point
                  afterColumn: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 税点（单位：%）
                  defaultValue: 0.00
              - column:
                  name: pay_type
                  afterColumn: amount
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账)
              - column:
                  name: real_pay_amount
                  afterColumn: real_amount
                  type: decimal(12,2)
                  remarks: 实际支付金额（单位：￥）
        - sql:
            sql: |
              update business_balance_prepay set pay_amount = amount,pay_type=5,real_pay_amount = real_amount;
  - changeSet:
      id: chp-1733102833
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: password
                  afterColumn: seed_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 密码
              - column:
                  name: phone
                  type: varchar(15)
                  remarks: 手机号
                  afterColumn: id
                  defaultValue: ''
        - sql:
            sql: |
              update distribution_channel a join biz_user b on b.id = a.biz_user_id set a.phone = b.phone, a.password = FLOOR(RAND() * 9000 + 1000);
        - dropIndex:
            tableName: distribution_channel
            indexName: uk_biz_user_id
        - createIndex:
            tableName: distribution_channel
            indexName: uk_seed_code
            unique: true
            columns:
              - column:
                  name: seed_code
        - dropColumn:
            tableName: distribution_channel
            columnName: user_name
  - changeSet:
      id: chp-1733363968
      author: chp
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: business_identifier
                  afterColumn: customer_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 商家标识 （0-新客 1-老客）
                  defaultValueNumeric: 0
        - sql:
            sql: |
              update business set business_identifier = 1;
  - changeSet:
      id: chp-1733370111
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `business`
              MODIFY COLUMN `business_identifier` tinyint(1) DEFAULT NULL COMMENT '商家标识 （0-新客 1-老客）' AFTER `customer_type`;
  - changeSet:
      id: chp-1733457600
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_member_channel`
              MODIFY COLUMN `channel_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '渠道手机号（biz_user.phone）' AFTER `channel_name`;

  - changeSet:
      id: chp-1733467603
      author: chp
      changes:
        - createTable:
            tableName: order_member_marketing_channel
            isNotExists: true
            remarks: 会员市场渠道记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id（marketing_channel.id）
              - column:
                  name: marketing_platform
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)
              - column:
                  name: channel_name
                  type: varchar(27)
                  constraints:
                    nullable: false
                  remarks: 渠道名称（marketing_channel.channel_name）
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: business_name
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: 商家名称（business.name）
              - column:
                  name: biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户id FK:biz_user.id
              - column:
                  name: biz_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户微信昵称
              - column:
                  name: biz_user_phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户手机号
              - column:
                  name: member_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 会员编码（business.member_code）
              - column:
                  name: member_package_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 套餐类型：0=季度会员,1=年度会员,2=三年会员
              - column:
                  name: real_pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单实付金额（单位：￥）
              - column:
                  name: remark
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 备注
                  defaultValue: ''
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 购买时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: chp-1733480177
      author: chp
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: order_num
                  afterColumn: member_package_type
                  type: varchar(30)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 订单号

  - changeSet:
      id: chp-1733796770
      author: chp
      changes:
        - createTable:
            tableName: marketing_channel_visit_flow
            isNotExists: true
            remarks: 市场渠道访问流水数据
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id（marketing_channel.id）
              - column:
                  name: unique_visitor
                  type: int(32)
                  constraints:
                    nullable: false
                  remarks: 独立访客
                  defaultValueNumeric: 0
              - column:
                  name: page_view
                  type: int(32)
                  constraints:
                    nullable: false
                  remarks: 访问量
                  defaultValueNumeric: 0
              - column:
                  name: bounce_rate
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 跳出率
                  defaultValueComputed: 0.00
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 采集时间
  - changeSet:
      id: chp-1733731486
      author: chp
      changes:
        - createTable:
            tableName: distribution_channel_order
            remarks: 分销渠道订单表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: seed_code
                  type: varchar(4)
                  constraints:
                    nullable: false
                  remarks: 种草码(distribution_channel.seed_code)
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 订单号
              - column:
                  name: order_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单金额
              - column:
                  name: real_pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单实付金额（单位：￥）
              - column:
                  name: pay_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 支付时间
  - changeSet:
      id: chp-1733744195
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `business_balance_flow`
              MODIFY COLUMN `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '修改人名称(FK:biz_user.nick_name、sys_user.user_name)' AFTER `create_user_id`,
              MODIFY COLUMN `update_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建人名称(FK:biz_user.nick_name、sys_user.user_name)' AFTER `update_user_id`;
  - changeSet:
      id: chp-1733983657
      author: chp
      changes:
        - addColumn:
            tableName: order_member_marketing_channel
            columns:
              - column:
                  name: order_num
                  afterColumn: member_package_type
                  type: varchar(30)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 订单号
  - changeSet:
      id: chp-1733984694
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_member_marketing_channel`
              MODIFY COLUMN `business_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '商家名称（business.name）' AFTER `business_id`;
              ALTER TABLE `order_member_marketing_channel`
              MODIFY COLUMN `real_pay_amount` decimal(12, 2) NULL DEFAULT '0.0' COMMENT '订单实付金额（单位：￥）' AFTER `order_num`;
  - changeSet:
      id: chp-1734068444
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `business`
              MODIFY COLUMN `business_identifier` tinyint(1) NULL DEFAULT 2 COMMENT '商家标识 （0-新客 1-老客 2-默认）' AFTER `customer_type`;
              update business set business_identifier = 2 where business_identifier is null;
  - changeSet:
      id: chp-1734327214
      author: chp
      changes:
        - addColumn:
            tableName: we_chat_external_user
            columns:
              - column:
                  name: channel_id
                  afterColumn: channel
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 渠道Id
              - column:
                  name: channel_type
                  type: tinyint(1)
                  remarks: 渠道类型(0=普通,1=市场，2=分销)
                  afterColumn: channel
                  defaultValue: 0
        - sql:
            sql: |
              UPDATE we_chat_external_user SET channel_type = 1, channel_id = SUBSTRING(channel, 15) WHERE channel LIKE "Sys-Marketing%";
              UPDATE we_chat_external_user SET channel_type = 2, channel_id = SUBSTRING(channel, 18) WHERE channel LIKE "Sys-Distribution%";

  - changeSet:
      id: chp-order-1734415863
      author: chp
      changes:
        - createTable:
            tableName: business_remark_flow
            isNotExists: true
            remarks: 商家备注流水
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: remark
                  type: varchar(1000)
                  remarks: 备注
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建人id（sys_user.user_id）
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 创建人名称（sys_user.user_name）
              - column:
                  name: creat_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间

  - changeSet:
      id: chp-1734416203
      author: chp
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: real_pay_amount
                  type: decimal(12,2)
                  afterColumn: member_package_type
                  defaultValueComputed: 0.00
                  constraints:
                    nullable: false
                  remarks: 订单实付金额（单位：￥）
              - column:
                  name: currency
                  type: tinyint(1)
                  afterColumn: member_package_type
                  defaultValue: 1
                  constraints:
                    nullable: false
                  remarks: 币种（1:人民币,2:离岸人民币,3:美元,4:澳元,5:加币,6:英镑,7:港币,8:日元,9:新西兰元,10:新加坡元）

  - changeSet:
      id: chp-1734425094
      author: chp
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: remark
                  type: varchar(1000)
                  afterColumn: member_validity
                  defaultValue: ''
                  remarks: 备注
  - changeSet:
      id: chp-1734504484
      author: chp
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: seed_code
                  type: varchar(10)
                  afterColumn: channel_phone
                  defaultValue: ''
                  remarks: 种草码
        - sql:
            sql: |
              update order_member_channel a join distribution_channel b on b.id = a.channel_id set a.seed_code = b.seed_code;


  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: account_id
                  type: bigint(20)
                  afterColumn: audit_user_name
                  constraints:
                    nullable: false
                  remarks: 收款账号ID
        - sql:
            sql: |
              ALTER TABLE `business_balance_prepay`
              MODIFY COLUMN `pay_type` tinyint(1) NOT NULL COMMENT '支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7-全币种)' AFTER `amount`;
  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - sql:
            sql: |
              update biz_user_channel buc
              		join(
              		select
              		bu.id,
              		wceu.channel_type
              		from biz_user bu
              		LEFT JOIN `we_chat_external_user` wceu ON wceu.external_userid = bu.external_user_id
              		where wceu.channel_type > 2
              		) bu on bu.id = buc.biz_user_id
              		set buc.register_channel_type = bu.channel_type


  - changeSet:
      id: chp-1735106447
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_flow
            columns:
              - column:
                  name: video_id
                  type: bigint(20)
                  afterColumn: video_code
                  constraints:
                    nullable: true
                  remarks: 视频订单ID
        - createTable:
            tableName: business_balance_detail
            isNotExists: true
            remarks: 商家余额详情表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: number
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 单号（退款审批号、预付审批单号）
              - column:
                  name: video_code
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 视频编码
                  defaultValue: ''
              - column:
                  name: number_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 1-退款订单,2-预付款订单
              - column:
                  name: origin
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、7.预付款收入)
              - column:
                  name: balance
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单余额
              - column:
                  name: use_balance
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 使用余额
                  defaultValueComputed: 0.00
              - column:
                  name: lock_balance
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 锁定余额
                  defaultValueComputed: 0.00
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人id
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 创建人名称
              - column:
                  name: creat_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 修改时间
        - createTable:
            tableName: business_balance_detail_log
            isNotExists: true
            remarks: 商家余额详情流水表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: balance_flow_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家余额流水ID（business_balance_flow.id）
              - column:
                  name: balance_number
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 余额单号（business_balance_detail.number）
              - column:
                  name: use_balance
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 使用余额
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 订单类型(0-收入、1-支出)
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人id
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 创建人名称
              - column:
                  name: creat_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
  - changeSet:
      id: chp-1735121296
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `business_balance_detail`
              CHANGE COLUMN `creat_time` `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间' AFTER `create_by`;
              ALTER TABLE `business_balance_detail_log`
              CHANGE COLUMN `creat_time` `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间' AFTER `create_by`;
  - changeSet:
      id: chp-1735184606
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_flow
            columns:
              - column:
                  name: flow_order_no
                  type: varchar(32)
                  afterColumn: business_id
                  constraints:
                    nullable: false
                  remarks: 余额流水号
                  defaultValue: ''
              - column:
                  name: prepay_num
                  type: varchar(32)
                  afterColumn: refund_num
                  constraints:
                    nullable: false
                  remarks: 预付单号
                  defaultValue: ''
              - column:
                  name: withdraw_number
                  type: varchar(32)
                  afterColumn: refund_num
                  constraints:
                    nullable: false
                  remarks: 提现单号
                  defaultValue: ''
        - addColumn:
            tableName: business_balance_detail_log
            columns:
              - column:
                  name: video_id
                  type: bigint(20)
                  afterColumn: balance_number
                  constraints:
                    nullable: true
                  remarks: 视频订单ID
              - column:
                  name: number
                  type: varchar(32)
                  afterColumn: balance_number
                  constraints:
                    nullable: false
                  remarks: 单号（订单号、提现单号、预付单号）
                  defaultValue: ''
  - changeSet:
      id: chp-1735198723
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_detail_log
            columns:
              - column:
                  name: video_code
                  type: varchar(30)
                  afterColumn: number
                  constraints:
                    nullable: false
                  remarks: 视频编码
                  defaultValue: ''
        - renameTable:
            oldTableName: business_balance_detail_log
            newTableName: business_balance_detail_flow

  - changeSet:
      id: chp-1735201388
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_detail
            columns:
              - column:
                  name: valid_balance
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 有效余额
                  afterColumn: lock_balance
                  defaultValueComputed: 0.00
  - changeSet:
      id: chp-1735208825
      author: chp
      changes:
        - createTable:
            tableName: business_balance_detail_lock
            isNotExists: true
            remarks: 商家余额详情锁定表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: number
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 锁定单号（提现单号、视频订单、会员订单）
              - column:
                  name: balance_detail_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家余额详情ID（business_balance_detail.id）
              - column:
                  name: use_balance
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 已用金额（business_balance_detail.use_balance + lock_balance 快照）
                  defaultValueComputed: 0.00
              - column:
                  name: pay_out_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 提现金额
                  defaultValueComputed: 0.00
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 状态（0:待处理,1:已提现,2.已取消）
                  defaultValue: 0
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 修改时间
  - changeSet:
      id: chp-1735279628
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_detail
            columns:
              - column:
                  name: origin_number
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 来源订单号（退款审批号、预付审批单号）
                  afterColumn: number
        - sql:
            sql: |
              ALTER TABLE `business_balance_detail`
              MODIFY COLUMN `number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单号（自动生成：TK、YF前缀）' AFTER `business_id`;  

  - changeSet:
      id: chp-1735371290
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_detail_lock
            columns:
              - column:
                  name: video_code
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 视频编码
                  afterColumn: balance_detail_id
                  defaultValue: ''
              - column:
                  name: prepay_num
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 预付单号
                  afterColumn: balance_detail_id
                  defaultValue: ''
              - column:
                  name: balance_number
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 余额详情单号
                  afterColumn: balance_detail_id
              - column:
                  name: balance_create_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 余额增加时间
                  afterColumn: status
              - column:
                  name: origin
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、7.预付款收入)
                  afterColumn: status


  - changeSet:
      id: chp-1735377036
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_audit_flow
            columns:
              - column:
                  name: payout_resource_url
                  type: varchar(300)
                  constraints:
                    nullable: false
                  remarks: 提现申请图片
                  afterColumn: pay_time
                  defaultValue: ''
  - changeSet:
      id: chp-1735390873
      author: chp
      changes:
        - createIndex:
            tableName: business_balance_detail
            indexName: uk_number
            unique: true
            columns:
              - column:
                  name: number


  - changeSet:
      id: chp-1735525033
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_detail
            columns:
              - column:
                  name: create_order_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 下单运营微信名
                  afterColumn: valid_balance
                  defaultValue: ''
              - column:
                  name: create_order_user_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 下单运营
                  afterColumn: valid_balance
                  defaultValue: ''


  - changeSet:
      id: chp-1735541273
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_detail_lock
            columns:
              - column:
                  name: create_order_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 下单运营微信名
                  afterColumn: balance_create_time
                  defaultValue: ''
              - column:
                  name: create_order_user_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 下单运营
                  afterColumn: balance_create_time
                  defaultValue: ''


  - changeSet:
      id: chp-1735628983
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_detail
            columns:
              - column:
                  name: pay_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
                  afterColumn: valid_balance
              - column:
                  name: order_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 下单时间
                  afterColumn: valid_balance


  - changeSet:
      id: chp-1735781911
      author: chp
      changes:
        - createIndex:
            tableName: business_balance_flow
            indexName: uk_flow_order_no
            unique: true
            columns:
              - column:
                  name: flow_order_no

  - changeSet:
      id: chp-1736388649
      author: chp
      changes:
        - createTable:
            tableName: user_model_blacklist
            isNotExists: true
            remarks: 模特黑名单
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 登录用户ID FK biz_user.id
              - column:
                  name: model_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特ID  FK model.id
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: blacklist_count
                  type: int(5)
                  constraints:
                    nullable: true
                  remarks: 拉黑数
                  defaultValueNumeric: 0
                  afterColumn: after_sale_rate
  - changeSet:
      id: chp-1736748792
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 渠道类型：2-分销渠道 7-裂变
                  defaultValueNumeric: 2
                  afterColumn: biz_user_id
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: channel_biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 登录用户ID FK distribution_channel.biz_user_id
                  afterColumn: channel_id
              - column:
                  name: channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 渠道类型：2-分销渠道 7-裂变
                  defaultValueNumeric: 2
                  afterColumn: channel_id

  - changeSet:
      id: chp-1736760587
      author: chp
      changes:
        - sql:
            sql: |
              UPDATE order_member_channel a
              JOIN business b on a.business_id = b.id
              set a.seed_code = b.seed_code where a.seed_code = '';

  - changeSet:
      id: chp-1736910687
      author: chp
      changes:
        - addColumn:
            tableName: biz_user
            columns:
              - column:
                  name: unbind_time
                  type: datetime
                  remarks: 解绑时间

  - changeSet:
      id: chp-1736993608
      author: chp
      changes:
        - createTable:
            tableName: business_owner_flow
            isNotExists: true
            remarks: 商家主账号换绑记录
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID(business.id)
              - column:
                  name: origin_account_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 原账号ID(business_account.account)
              - column:
                  name: origin_account_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 原账号微信名(biz_user.nick_name)
              - column:
                  name: origin_account_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 原账号名称(biz_user.name)
              - column:
                  name: account_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 更换账号ID(business_account.account)
              - column:
                  name: account_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 更换账号微信名(biz_user.nick_name)
              - column:
                  name: account_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 更换账号名称(biz_user.name)
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 处理人（sys_user.user_id）
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 处理人名称（sys_user.user_name）
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 处理时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 修改时间


  - changeSet:
      id: chp-1737020548
      author: chp
      changes:
        - createTable:
            tableName: business_member_activity
            isNotExists: true
            remarks: 商家会员活动
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: member_package_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 套餐类型：0-季度套餐,1-年度套餐,2-三年套餐
              - column:
                  name: presented_time
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 加赠时间
                  defaultValue: 0
              - column:
                  name: presented_time_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 加赠时间类型（1-天,2-月,3-年）
              - column:
                  name: start_time
                  type: tinyint(4)
                  constraints:
                    nullable: false
                  remarks: 活动开始时间
              - column:
                  name: end_time
                  type: tinyint(4)
                  constraints:
                    nullable: false
                  remarks: 活动结束时间
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否有效（1-有效， 0-无效）
                  defaultValue: 1
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建人（sys_user.user_id）
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 创建人名称（sys_user.user_name）
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建人时间
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 修改人（sys_user.user_id）
              - column:
                  name: update_by
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 修改人名称（sys_user.user_name）
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 修改时间
        - sql:
            sql: |
              ALTER TABLE `business_member_activity`
              MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by`;

  - changeSet:
      id: chp-order-1737082722
      author: chp
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: presented_time_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  afterColumn: presented_time
                  remarks: 加赠时间类型（1-天,2-月,3-年）
                  defaultValue: 2
        - sql:
            sql: |
              ALTER TABLE `business_member_validity_flow`
              MODIFY COLUMN `presented_time` tinyint(4) NULL DEFAULT 0 COMMENT '加赠时间（单位：月）' AFTER `result_member_validity`;
  - changeSet:
      id: chp-1737336473
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: activation_time
                  type: datetime
                  remarks: 激活时间
                  afterColumn: status
              - column:
                  name: activation_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 激活状态（1-有效， 0-无效）
                  defaultValue: 0
                  afterColumn: status
  - changeSet:
      id: chp-1737421717
      author: chp
      changes:
        - createTable:
            tableName: business_channel
            isNotExists: true
            remarks: 商家渠道信息表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 用户ID (FK:business.id)
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 用户ID (FK:biz_user.id)
              - column:
                  name: register_channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 注册渠道类型(0=普通,1=分销，2=市场,7=裂变)
                  defaultValueNumeric: 0
              - column:
                  name: register_channel_id
                  type: bigint(20)
                  remarks: 注册渠道账户id
              - column:
                  name: register_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 注册时间
              - column:
                  name: wechat_channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 企微渠道类型(0=普通,1=分销，2=市场,7=裂变)
                  defaultValueNumeric: 0
              - column:
                  name: wechat_channel_id
                  type: bigint(20)
                  remarks: 添加企微渠道账户id
              - column:
                  name: add_wechat_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 添加企微时间
        - createIndex:
            tableName: business_channel
            indexName: uk_business_id
            unique: true
            columns:
              - column:
                  name: business_id

  - changeSet:
      id: chp-order-1737421794
      author: chp
      changes:
        - sql:
            sql: |
              INSERT INTO business_channel
              SELECT
              	t.*
              FROM
              	(
              	SELECT NULL,
              		ba.business_id,
              		buc.biz_user_id,
              		buc.register_channel_type,
              		buc.register_channel_id,
              		buc.register_time,
              		buc.wechat_channel_type,
              		buc.wechat_channel_id,
              		buc.add_wechat_time
              	FROM
              		biz_user_channel buc
              		INNER JOIN biz_user bu on bu.id = buc.biz_user_id
              		INNER join business_account ba on ba.biz_user_id = bu.id
              	WHERE ba.is_owner_account = 1)t

  - changeSet:
      id: chp-order-**********
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `business_balance_prepay`
              MODIFY COLUMN `account_id` bigint(16) NULL DEFAULT NULL COMMENT '收款账号ID' AFTER `audit_user_name`;


  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: current_exchange_rate
                  type: decimal(12,4)
                  constraints:
                    nullable: true
                  afterColumn: real_pay_amount
                  remarks: 当前汇率
              - column:
                  name: currency
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  afterColumn: real_pay_amount
                  remarks: 币种（详见sys_dict_type.dict_type = sys_money_type）
              - column:
                  name: real_pay_amount_currency
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  afterColumn: real_pay_amount
                  remarks: 实际支付金额（对应币种实付）

  - changeSet:
      id: chp-1738738353
      author: chp
      changes:
        - sql:
            sql: |
              update business_balance_prepay
              set real_pay_amount_currency = real_pay_amount,
                  currency = 1,
                  current_exchange_rate = 7.2345
              where pay_type = 7 and currency is null
  - changeSet:
      id: chp-1738744315
      author: chp
      changes:
        - sql:
            sql: |
              update business_balance_prepay
              set currency = null,
                  current_exchange_rate = null
              where pay_type = 7 and currency is not null and audit_status != 1
  - changeSet:
      id: chp-1738997089
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `business_balance_detail_flow`
              MODIFY COLUMN `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建人名称' AFTER `create_by_id`;
              ALTER TABLE `business_balance_detail`
              MODIFY COLUMN `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建人名称' AFTER `create_by_id`;

  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - sql:
            sql: |
              ALTER TABLE `business_account_apply`
              MODIFY COLUMN `nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '微信昵称' AFTER `name`;


  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: contain_presented_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  afterColumn: amount
                  defaultValueComputed: 0.00
                  remarks: 包含赠送金额（单位：￥）
  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: change_reason_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  afterColumn: real_pay_amount
                  remarks: 修改原因类型（1:老会员入驻,2:七天无理由,3:退会,4:其他）

  - changeSet:
      id: chp-1739858688
      author: chp
      changes:
        - sql:
            update business_member_validity_flow
            set change_reason_type = 4
            where type = 0 and change_reason_type is null

  - changeSet:
      id: chp-1739864849
      author: chp
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: real_settle_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 实际结算金额
                  defaultValue: 0.00
  - changeSet:
      id: chp-1739864850
      author: chp
      changes:
        - sql:
            update order_member_channel
            set real_settle_amount = settle_amount
            where settle_status = 1;
            UPDATE `order_member_channel`
            SET `settle_status` = 2
            WHERE `settle_status` = 0
            AND `create_time` >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY);
  - changeSet:
      id: chp-1739873305
      author: chp
      changes:
        - sql:
            ALTER TABLE `order_member_channel`
            MODIFY COLUMN `settle_status` tinyint(1) NOT NULL DEFAULT 2 COMMENT '状态（0-未结算，1-已结算,2-待结算,3-不可结算）' AFTER `settle_time`;

  - changeSet:
      id: chp-1739937136
      author: chp
      changes:
        - sql:
            ALTER TABLE `order_member_channel`
            MODIFY COLUMN `real_settle_amount` decimal(12, 2) NULL COMMENT '实际结算金额' AFTER `settle_amount`;
            update order_member_channel
            set real_settle_amount = null
            where settle_status != 1;
  - changeSet:
      id: chp-1739952365
      author: chp
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: pay_succeed
                  type: tinyint(1)
                  constraints:
                    nullable: fales
                  remarks: 是否下单成功（0-未成功，1-下单成功）
                  defaultValueNumeric: 0

  - changeSet:
      id: chp-1739957818
      author: chp
      changes:
        - sql:
            update business set pay_succeed = 1 where recent_order_time is not null;

  - changeSet:
      id: chp-1741947523
      author: chp
      changes:
        - createTable:
            tableName: translation_cache
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: source_text
                  type: TEXT
                  constraints:
                    nullable: false
                  remarks: 原文
              - column:
                  name: translated_text
                  type: TEXT
                  constraints:
                    nullable: false
                  remarks: 译文
              - column:
                  name: source_sha256
                  type: VARCHAR(64)
                  remarks: 原文sha256
                  constraints:
                    nullable: false
                    unique: true
                    uniqueConstraintName: uq_source_sha256

  - changeSet:
      id: chp-1742264239
      author: chp
      changes:
        - sql:
            ALTER TABLE `amazon_goods_pic`
            MODIFY COLUMN `goods_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '亚马逊商品id' AFTER `id`;
            ALTER TABLE `amazon_goods_pic`
            MODIFY COLUMN `object_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'OSS的相对路径' AFTER `product_name`;

  - changeSet:
      id: chp-1744277971
      author: chp
      changes:
        - sql:
            ALTER TABLE `business_balance_prepay`
            MODIFY COLUMN `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态（0:待处理,1:审核通过,2.审核拒绝,3.已关闭）' AFTER `resource_id`;
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: pay_num
                  type: varchar(30)
                  remarks: 支付单号
                  afterColumn: business_id
                  constraints:
                    nullable: true
              - column:
                  name: order_type
                  type: tinyint(1)
                  remarks: 订单类型（3-线下钱包充值，5-线上钱包充值）
                  defaultValue: 3
                  afterColumn: prepay_num
                  constraints:
                    nullable: false
              - column:
                  name: status
                  type: tinyint(1)
                  remarks: 订单状态（1:待支付,2:待审核,8:已完成,9:交易关闭）
                  defaultValue: 1
                  afterColumn: resource_id
                  constraints:
                    nullable: false
              - column:
                  name: pay_user_id
                  type: bigint
                  remarks: 支付用户id FK business_account.id
                  afterColumn: current_exchange_rate
                  constraints:
                    nullable: true
              - column:
                  name: alipay_pay_app_id
                  type: varchar(50)
                  remarks: 支付宝支付主体appid
                  afterColumn: pay_time
                  constraints:
                    nullable: true
              - column:
                  name: wechat_pay_app_id
                  type: varchar(50)
                  remarks: 微信支付主体appid
                  afterColumn: pay_time
                  constraints:
                    nullable: true
              - column:
                  name: submit_credential_time
                  type: datetime
                  remarks: 提交凭证时间
                  afterColumn: resource_id
                  constraints:
                    nullable: true
              - column:
                  name: close_order_time
                  type: datetime
                  remarks: 关闭订单时间
                  afterColumn: resource_id
                  constraints:
                    nullable: true


  - changeSet:
      id: chp-1744277977
      author: chp
      changes:
        - sql:
            update business_balance_prepay set submit_credential_time = creat_time;
            update business_balance_prepay set status = 2 where audit_status = 0;
            update business_balance_prepay set status = 8 where audit_status in (1,2);


  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - sql:
            ALTER TABLE `business_balance_prepay`
            MODIFY COLUMN `pay_type` tinyint(1) NULL COMMENT '支付方式(1:微信,2:支付宝支付,5:银行卡转账,6:对公转账,7-全币种)' AFTER `contain_presented_amount`;


  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: pay_account
                  type: varchar(100)
                  remarks: 付款账号名称
                  afterColumn: pay_time
                  constraints:
                    nullable: true
  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: pay_amount_dollar
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 应付金额（单位：$）
                  defaultValueComputed: 0.00
                  afterColumn: pay_amount

  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - sql:
            update business_balance_prepay set status = 9 where audit_status = 2;

  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: seed_id
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 种草官ID
                  afterColumn: seed_code
                  defaultValue: ''

  - changeSet:
      id: chp-1747212350
      author: chp
      changes:
        - createTable:
            tableName: member_seed_record
            isNotExists: true
            remarks: 会员种草记录
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id（distribution_channel.id）
              - column:
                  name: channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 渠道类型：2-分销渠道 7-裂变
                  defaultValueNumeric: 2
              - column:
                  name: channel_biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 登录用户ID FK distribution_channel.biz_user_id
              - column:
                  name: channel_name
                  type: varchar(27)
                  constraints:
                    nullable: false
                  remarks: 渠道名称（distribution_channel.channel_name）
              - column:
                  name: channel_phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 渠道手机号（biz_user.phone）
              - column:
                  name: channel_seed_id
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 种草官ID（distribution_channel.seed_id）
              - column:
                  name: seed_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 种草码
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家id（business.id）
              - column:
                  name: business_name
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: 商家名称（business.name）
              - column:
                  name: biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户id FK:biz_user.id
              - column:
                  name: biz_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户微信昵称
              - column:
                  name: biz_user_phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户手机号
              - column:
                  name: member_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 会员编码（business.member_code）
              - column:
                  name: member_package_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 套餐类型：0=季度会员,1=年度会员,2=三年会员
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  defaultValue: ''
                  remarks: 订单号
              - column:
                  name: real_pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单实付金额（单位：￥）
              - column:
                  name: currency
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 币种（详见sys_dict_type.dict_type = sys_money_type）
              - column:
                  name: real_pay_amount_currency
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 实际支付金额（对应币种实付）
              - column:
                  name: pay_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
              - column:
                  name: settle_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 结算类型（1-固定金额，2-固定比例）
                  defaultValue: 1
              - column:
                  name: settle_rage
                  type: decimal(12,1)
                  constraints:
                    nullable: false
                  remarks: 结算比例
                  defaultValue: 0.0
              - column:
                  name: seed_code_discount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 种草码优惠金额(固定金额)
                  defaultValue: 0.0
              - column:
                  name: settle_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 最终结算金额（结算比例 * 会员价格）
                  defaultValue: 0.00
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 状态（0-待入账，1-带提现，2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常，99-入账失败）
                  defaultValueNumeric: 0
              - column:
                  name: audit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 审核时间
              - column:
                  name: withdrawal_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 打款时间
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: false
                  remarks: 原因(不通过理由、异常理由、入账失败理由)
                  defaultValue: ''
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 购买时间
                  defaultValueComputed: CURRENT_TIMESTAMP

  - changeSet:
      id: chp-1747215078
      author: chp
      changes:
        - createTable:
            tableName: member_seed_record_withdrawal
            isNotExists: true
            remarks: 会员种草提现
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: withdrawal_num
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 提现单号
              - column:
                  name: channel_seed_id
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 种草官ID（distribution_channel.seed_id）
              - column:
                  name: settle_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 申请结算金额（所有种草记录和）
                  defaultValue: 0.00
              - column:
                  name: withdrawal_account_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 提现账号类型(2-支付宝，3-银行卡)
              - column:
                  name: payee_name
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 收款方姓名
              - column:
                  name: payee_phone
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 收款方手机号
              - column:
                  name: payee_identity_card
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 收款方身份证号
              - column:
                  name: payee_account
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 收款方账号
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 状态（2-待审核，3-待打款，4-已打款，5-审核不通过，6-打款异常）
                  defaultValueNumeric: 2
              - column:
                  name: audit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 审核时间
              - column:
                  name: audit_remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 审核备注
              - column:
                  name: audit_user_id
                  type: bigint(20)
                  remarks: 审核人员id FK sys_user.user_id
              - column:
                  name: audit_user_name
                  type: varchar(32)
                  remarks: 审核人员名称
              - column:
                  name: withdrawal_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 提现时间
              - column:
                  name: withdrawal_remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 提现备注
              - column:
                  name: withdrawal_user_id
                  type: bigint(20)
                  remarks: 提现人员id FK sys_user.user_id
              - column:
                  name: withdrawal_user_name
                  type: varchar(32)
                  remarks: 提现人员名称
              - column:
                  name: pay_account
                  type: varchar(32)
                  remarks: 打款账号
              - column:
                  name: resource_url
                  type: varchar(500)
                  remarks: 打款凭证url
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 申请时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: false

  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - createTable:
            tableName: member_seed_record_relevance
            isNotExists: true
            remarks: 会员种草提现_种草记录关联表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: member_seed_record_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 种草记录id FK member_seed_record.id
              - column:
                  name: member_seed_record_withdrawal_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 会员种草提现id  FK member_seed_record_withdrawal.id
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间

  - changeSet:
      id: chp-1747218573
      author: chp
      changes:
        - addColumn:
            tableName: biz_user
            columns:
              - column:
                  name: seed_id
                  type: varchar(16)
                  constraints:
                    nullable: false
                  remarks: 种草官ID
                  afterColumn: unbind_time
                  defaultValue: ''


  - changeSet:
      id: chp-1747218716
      author: chp
      changes:
        - createTable:
            tableName: distribution_channel_discount_log
            isNotExists: true
            remarks: 渠道折扣日志
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 渠道类型：2-分销渠道 7-裂变
                  defaultValueNumeric: 2
              - column:
                  name: member_discount_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 会员折扣类型（1-固定金额，2-固定比例）
                  defaultValue: 1
              - column:
                  name: member_discount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 会员折扣
                  defaultValue: 0.00
              - column:
                  name: settle_discount_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 结算佣金类型（1-固定金额，2-固定比例）
                  defaultValue: 1
              - column:
                  name: settle_discount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 结算佣金
                  defaultValue: 0.00
              - column:
                  name: start_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 活动开始时间
              - column:
                  name: end_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 活动结束时间
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间


  - changeSet:
      id: chp-1747279691
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: settle_discount_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 结算佣金类型（1-固定金额，2-固定比例）
                  defaultValue: 2
                  afterColumn: poster_name

  - changeSet:
      id: chp-1747385185
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: disable_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 禁用时间
                  afterColumn: status

  - changeSet:
      id: chp-1747634539
      author: chp
      changes:
        - addColumn:
            tableName: member_seed_record_withdrawal
            columns:
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id（distribution_channel.id）
                  afterColumn: withdrawal_num
              - column:
                  name: channel_seed_code
                  type: varchar(4)
                  constraints:
                    nullable: false
                  remarks: 种草码（distribution_channel.seed_code）
                  afterColumn: channel_seed_id
  - changeSet:
      id: chp-1747704562
      author: chp
      changes:
        - createIndex:
            tableName: member_seed_record_withdrawal
            indexName: uk_withdrawal_num
            unique: true
            columns:
              - column:
                  name: withdrawal_num
  - changeSet:
      id: chp-1747792616
      author: chp
      changes:
        - sql:
            ALTER TABLE `member_seed_record`
            MODIFY COLUMN `business_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '商家名称（business.name）' AFTER `business_id`;
            ALTER TABLE `member_seed_record_withdrawal`
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;

  - changeSet:
      id: chp-1747807307
      author: chp
      changes:
        - sql:
            ALTER TABLE `distribution_channel`
            MODIFY COLUMN `disable_time` datetime(0) NULL DEFAULT NULL COMMENT '禁用时间/启用时间' AFTER `status`;
  - changeSet:
      id: chp-1747807283
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel_discount_log
            columns:
              - column:
                  name: create_by
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 创建人名称（sys_user.user_name）
                  afterColumn: end_time
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人（sys_user.user_id）
                  afterColumn: end_time
  - changeSet:
      id: chp-1747813360
      author: chp
      changes:
        - sql:
            ALTER TABLE `member_seed_record_withdrawal`
            MODIFY COLUMN `withdrawal_time` datetime(0) NULL DEFAULT NULL COMMENT '提现审核时间' AFTER `audit_user_name`;
        - addColumn:
            tableName: member_seed_record_withdrawal
            columns:
              - column:
                  name: payout_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 打款时间
                  afterColumn: audit_user_name
  - changeSet:
      id: chp-1747814055
      author: chp
      changes:
        - sql:
            ALTER TABLE `member_seed_record`
            DROP COLUMN `audit_time`,
            DROP COLUMN `withdrawal_time`,
            DROP COLUMN `remark`;
  - changeSet:
      id: chp-1747817980
      author: chp
      changes:
        - addColumn:
            tableName: member_seed_record_withdrawal
            columns:
              - column:
                  name: channel_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 渠道类型：2-分销渠道 7-裂变
                  defaultValueNumeric: 7
                  afterColumn: channel_id
  - changeSet:
      id: chp-1748249645
      author: chp
      changes:
        - sql:
            ALTER TABLE `member_seed_record`
            MODIFY COLUMN `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间' AFTER `status`;
        - addColumn:
            tableName: member_seed_record
            columns:
              - column:
                  name: buy_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 购买时间
                  defaultValueComputed: CURRENT_TIMESTAMP
                  afterColumn: status
  - changeSet:
      id: chp-1748595639
      author: chp
      changes:
        - sql:
            delete from distribution_channel_discount_log where 1=1;

  - changeSet:
      id: chp-1748931643
      author: chp
      changes:
        - sql:
            UPDATE distribution_channel
            SET seed_id = 100000 + id
            WHERE  seed_id = ''

  - changeSet:
      id: chp-1749021553
      author: chp
      changes:
        - addColumn:
            tableName: distribution_channel_discount_log
            columns:
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id（distribution_channel.id）
                  afterColumn: id

  - changeSet:
      id: chp-1750139471
      author: chp
      changes:
        - createTable:
            tableName: business_member_data_statistics
            isNotExists: true
            remarks: 商家会员数据统计
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: record_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间
              - column:
                  name: member_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 新会员数
              - column:
                  name: renew_member_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 续费会员数
              - column:
                  name: exit_member_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 退回会员数
              - column:
                  name: expire_member_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 到期会员数
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false


  - changeSet:
      id: chp-1750140538
      author: chp
      changes:
        - createTable:
            tableName: business_order_data_statistics
            isNotExists: true
            remarks: 商家排单数统计
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: record_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID
              - column:
                  name: business_member_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 会员类型(1-新会员,2-老会员)
              - column:
                  name: video_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 订单数量
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
  - changeSet:
      id: chp-1750141041
      author: chp
      changes:
        - sql:
            ALTER TABLE `business_member_data_statistics`
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;
            ALTER TABLE `business_order_data_statistics`
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;

  - changeSet:
      id: chp-1750142171
      author: chp
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: recharge_count
                  type: int(11)
                  constraints:
                    nullable: false
                  remarks: 购买会员次数
                  defaultValueNumeric: 0
                  afterColumn: member_validity

  - changeSet:
      id: chp-1750142391
      author: chp
      changes:
        - sql:
            UPDATE business b
            JOIN  (
            select
            business_id,
            count(business_id) rechargeCount
            from business_member_validity_flow
            where type = 1
            group by business_id
            ) a on b.id = a.business_id
            set b.recharge_count = a.rechargeCount;
            UPDATE business b
            set b.recharge_count = b.recharge_count -1
            where b.id in(select business_id from business_member_validity_flow where change_reason_type = 2);


  - changeSet:
      id: chp-1750387075
      author: chp
      changes:
        - sql: |
            delete from business_member_data_statistics where 1=1;
            INSERT INTO business_member_data_statistics (
                record_time,
                member_count,
                renew_member_count,
                exit_member_count,
                expire_member_count,
                create_time,
                update_time
            )
            WITH RECURSIVE date_series AS (
                SELECT CAST('2024-10-22 00:00:00' AS DATETIME) AS dt
                UNION ALL
                SELECT DATE_ADD(dt, INTERVAL 1 DAY)
                FROM date_series
                WHERE dt < DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
            )
            SELECT
                dt,
                0,
                0,
                0,
                0,
                dt,
                dt
            FROM date_series;
            update business_member_data_statistics a
            INNER JOIN (
            SELECT
                DATE(member_validity) AS groupDate,
                COUNT(*) AS valid_business_count
            FROM
                business
            WHERE
                 member_validity IS NOT NULL
            GROUP BY
                groupDate
            ORDER BY groupDate
            )b on b.groupDate = DATE(a.record_time)
            set a.expire_member_count = b.valid_business_count;


            update business_member_data_statistics a
            INNER JOIN (
            SELECT
                DATE(member_first_time) AS groupDate,
                COUNT(*) AS valid_business_count
            FROM
            	business
            	WHERE
                 member_first_time IS NOT NULL
            GROUP BY
                groupDate
            ORDER BY groupDate
            )b on b.groupDate = DATE(a.record_time)
            set a.member_count = b.valid_business_count;

            update business_member_data_statistics a
            INNER JOIN (
            SELECT
                DATE(create_time) AS groupDate,
                COUNT(DISTINCT business_id) AS valid_business_count
            FROM
                business_member_validity_flow
            WHERE
                 change_reason_type in(2,3)
            GROUP BY
                groupDate
            ORDER BY groupDate
            )b on b.groupDate = DATE(a.record_time)
            set a.exit_member_count = b.valid_business_count;


            UPDATE business_member_data_statistics a
            INNER JOIN (
            	SELECT
            		DATE( bm.create_time ) AS groupDate,
            		COUNT(*) AS valid_business_count
            	FROM
            		business_member_validity_flow bm
            	WHERE
            		bm.type = 1
            		AND bm.create_time IS NOT NULL
            	GROUP BY
            		groupDate
            	) b ON b.groupDate = DATE( a.record_time )
            	SET a.renew_member_count = b.valid_business_count;

            UPDATE business_member_data_statistics
            SET renew_member_count = renew_member_count - member_count;


  - changeSet:
      id: chp-1750665579
      author: chp
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: recharge_count
                  type: int(11)
                  constraints:
                    nullable: false
                  remarks: 购买会员次数
                  defaultValueNumeric: 0
                  afterColumn: type

  - changeSet:
      id: chp-**********
      author: chp
      changes:
        - addColumn:
            tableName: member_seed_record_withdrawal
            columns:
              - column:
                  name: bank_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 开户行名称
                  afterColumn: payee_identity_card
        - sql:
            ALTER TABLE `member_seed_record_withdrawal`
            MODIFY COLUMN `withdrawal_account_type` tinyint(1) NOT NULL COMMENT '提现账号类型(2-支付宝，3-银行卡，6-公户收款)' AFTER `settle_amount`;
            ALTER TABLE `member_seed_record_withdrawal`
            MODIFY COLUMN `payee_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '收款方姓名' AFTER `withdrawal_account_type`;
            ALTER TABLE `member_seed_record_withdrawal`
            MODIFY COLUMN `payee_identity_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收款方身份证号' AFTER `payee_phone`;