databaseChangeLog:
  - logicalFilePath: 'changelog-dwy-1.0.yml'
  - changeSet:
      id: dwy1
      author: dwy
      changes:
        # 删除非空约束
        - dropNotNullConstraint:
            columnName: birthday
            columnDataType: date
            tableName: model
  - changeSet:
      id: dwy2
      author: dwy
      changes:
        # 设置列备注
        - setColumnRemarks:
            tableName: model
            columnName: birthday
            columnDataType: date
            remarks: 出生日期
  - changeSet:
      id: dwy-business-3
      author: dwy
      changes:
        - createIndex:
            tableName: logistic
            indexName: uk_number
            unique: true
            columns:
              - column:
                  name: number
        - createIndex:
            tableName: model_person
            indexName: uk_model_id
            unique: true
            columns:
              - column:
                  name: model_id
  - changeSet:
      id: dwy-business-4
      author: dwy
      changes:
        - sql:
            sql: |
              ALTER TABLE `model` 
              MODIFY COLUMN `platform` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '平台(0:<PERSON>,1:tiktok,2:其他,3:APP/解说类)' AFTER `tiktok_video`;
  - changeSet:
      id: dwy-business-5
      author: dwy
      changes:
        - sql:
            sql:  |
              ALTER TABLE `model` 
              MODIFY COLUMN `type` tinyint(1) NULL DEFAULT 0 COMMENT '模特类型(0:影响者,1:素人)' AFTER `id`,
              MODIFY COLUMN `pic` bigint(20) NULL DEFAULT NULL COMMENT '模特图片（FK:resource.id）' AFTER `source_from`,
              MODIFY COLUMN `live_pic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生活场景照（FK:resource.id 多个,隔开）' AFTER `pic`,
              MODIFY COLUMN `amazon_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '亚马逊案例视频（FK:resource.id 多个,隔开）' AFTER `live_pic`,
              MODIFY COLUMN `tiktok_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'tiktok案例视频（FK:resource.id 多个,隔开）' AFTER `amazon_video`;
  - changeSet:
      id: dwy-business-6
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business` 
            MODIFY COLUMN `balance` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '帐号余额' AFTER `customer_type`;
        - sql: |
            ALTER TABLE `business_balance_flow` 
            MODIFY COLUMN `amount` decimal(12, 2) NOT NULL COMMENT '订单金额（单位：￥）' AFTER `balance`;
        - setColumnRemarks:
            tableName: model
            columnName: commission
            columnDataType: decimal(12,2)
            remarks: 模特佣金
  - changeSet:
      id: dwy-business-7
      author: dwy
      changes:
        - createTable:
            tableName: amazon_goods_pic
            isNotExists: true
            remarks: 亚马逊_商品图片表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: goods_id
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 亚马逊商品id
              - column:
                  name: resource_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 亚马逊商品图片资源id FK:resource.id
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-business-8
      author: dwy
      changes:
        - createIndex:
            tableName: amazon_goods_pic
            indexName: uk_goods_id
            unique: true
            columns:
              - name: goods_id
        - createIndex:
            tableName: amazon_goods_pic
            indexName: uk_resource_id
            unique: true
            columns:
              - name: resource_id
        - sql:
            sql: |
              ALTER TABLE `amazon_goods_pic` 
              MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-business-9
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: overtime_rate
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 超时率
                  defaultValue: 0.00
              - column:
                  name: after_sale_rate
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 售后率
                  defaultValue: 0.00
  - changeSet:
      id: dwy-business-10
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            MODIFY COLUMN `overtime_rate` decimal(12, 2) NULL DEFAULT 0.0 COMMENT '超时率' AFTER `top_time`,
            MODIFY COLUMN `after_sale_rate` decimal(12, 2) NULL DEFAULT 0.0 COMMENT '售后率' AFTER `overtime_rate`;
  - changeSet:
      id: dwy-business-11
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_travel` 
            DROP COLUMN `status`,
            MODIFY COLUMN `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '行程原因' AFTER `end_time`;
  - changeSet:
      id: dwy-business-12
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT 0 COMMENT '模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)' AFTER `commission`;
  - changeSet:
      id: dwy-business-13
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `tag` 
            MODIFY COLUMN `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称' AFTER `parent_id`;
            ALTER TABLE `tag_category` 
            MODIFY COLUMN `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称' AFTER `path`;
  - changeSet:
      id: dwy-business-14
      author: dwy
      changes:
        - createTable:
            tableName: text_table
            isNotExists: true
            remarks: 文本表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 文本名称
              - column:
                  name: content
                  type: text
                  constraints:
                    nullable: true
                  remarks: 文本内容
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: version
                  type: float(10,1)
                  constraints:
                    nullable: true
                  remarks: 版本号
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        - createTable:
            tableName: text_table_history
            isNotExists: true
            remarks: 文本_历史记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: text_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 文本表主键 (FK:text_table.id)
              - column:
                  name: name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 文本名称
              - column:
                  name: content
                  type: text
                  constraints:
                    nullable: true
                  remarks: 文本内容
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: version
                  type: float(10,1)
                  constraints:
                    nullable: true
                  remarks: 版本号
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-business-15
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `text_table` 
            MODIFY COLUMN `version` int NULL DEFAULT NULL COMMENT '版本号' AFTER `remark`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
            ALTER TABLE `text_table_history` 
            MODIFY COLUMN `version` int NULL DEFAULT NULL COMMENT '版本号' AFTER `remark`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-business-16
      author: dwy
      changes:
        - sql: |
            INSERT INTO text_table ( id, `name`, `version` ) VALUES( 1, '《用户协议》', 1 ),(2,'《支付协议》',1),(3,'《隐私协议》',1),(4,'《平台协议》',1);
            INSERT INTO text_table_history ( id, text_id, `name`, `version` ) VALUES( 1, 1, '《用户协议》', 1 ),(2,2,'《支付协议》',1),(3,3,'《隐私协议》',1),(4,4,'《平台协议》',1);
  - changeSet:
      id: dwy-business-17
      author: dwy
      changes:
        - addColumn:
            tableName: text_table
            columns:
              - column:
                  name: can_delete
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValue: 0
                  remarks: 是否可删除（0:可以,1:不可以）
  - changeSet:
      id: dwy-business-18
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `text_table` 
            MODIFY COLUMN `can_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可删除（0:可以,1:不可以）' AFTER `version`;
  - changeSet:
      id: dwy-business-19
      author: dwy
      changes:
        - sql: |
            UPDATE text_table SET can_delete = 1 WHERE id IN (1,2,3,4)
  - changeSet:
      id: dwy-business-20
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `text_table` 
            MODIFY COLUMN `version` int(11) NULL DEFAULT 1 COMMENT '版本号' AFTER `remark`;
  - changeSet:
      id: dwy-business-21
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `text_table_history` 
            MODIFY COLUMN `version` int(11) NULL DEFAULT 1 COMMENT '版本号' AFTER `remark`;
  - changeSet:
      id: dwy-business-22
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `amazon_goods_pic` 
            CHANGE COLUMN `resource_id` `object_key` varchar(64) NOT NULL COMMENT 'OSS的相对路径' AFTER `goods_id`;
  - changeSet:
      id: 1725533750
      author: fzw
      changes:
        - createIndex:
            tableName: model_travel
            indexName: uk_model_id
            unique: true
            columns:
              - column:
                  name: model_id
      comment: 创建模特行程表模特id唯一索引，避免重复行程，原dwy-business-23 ~ dwy-business-25冲突，结构一致，但迁移后mysql无法使用，故新增
  - changeSet:
      id: dwy-business-26
      author: dwy
      changes:
      - createTable:
          tableName: biz_resource
          isNotExists: true
          remarks: 业务_资源表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: object_key
                type: varchar(64)
                constraints:
                  nullable: false
                remarks: 资源URI
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
                defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-business-27
      author: dwy
      changes:
      - addColumn:
          tableName: model
          columns:
            - column:
                name: model_pic
                type: varchar(64)
                constraints:
                  nullable: true
                remarks: 模特头图
                afterColumn: pic
  - changeSet:
      id: dwy-business-28
      author: dwy
      changes:
        - setTableRemarks:
            tableName: resource
            remarks: 模特案例视频资源表
        - renameTable:
            oldTableName: resource
            newTableName: model_video_resource
  - changeSet:
      id: dwy-business-29
      author: dwy
      changes:
        - dropColumn:
            tableName: model
            columns:
              - column:
                  name: model_pic
  - changeSet:
      id: dwy-business-30
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `model` 
          CHANGE COLUMN `pic` `model_pic` varchar(64) NULL DEFAULT NULL COMMENT '模特头图URI' AFTER `source_from`,
          MODIFY COLUMN `amazon_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '亚马逊案例视频（FK:model_video_resource.id 多个,隔开）' AFTER `live_pic`,
          MODIFY COLUMN `tiktok_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'tiktok案例视频（FK:model_video_resource.id 多个,隔开）' AFTER `amazon_video`;
  - changeSet:
      id: dwy-business-31
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `model` 
          MODIFY COLUMN `live_pic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生活场景照（FK:biz.resource.id 多个,隔开）' AFTER `model_pic`;
  - changeSet:
      id: dwy-business-32
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `model_video_resource` 
          CHANGE COLUMN `pic_url` `pic_uri` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片URI' AFTER `video_url`;
  - changeSet:
      id: dwy-business-33
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: status_explain
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 状态说明
                  afterColumn: status
  - changeSet:
      id: dwy-business-34
      author: dwy
      changes:
        - createTable:
            tableName: model_change_record
            isNotExists: true
            remarks: 模特信息变更记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: model_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特id
              - column:
                  name: operate_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 操作时间
              - column:
                  name: operate_object
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 操作对象（1:商家,2:运营,3:模特,9:系统）
              - column:
                  name: operate_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 操作人id
              - column:
                  name: operate_user_name
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 操作人姓名
              - column:
                  name: operate_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 操作类型（1:新增模特,2:修改模特信息,3:变更状态）
              - column:
                  name: operate_detail
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 操作详情
              - column:
                  name: operate_explain
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 操作说明
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-business-35
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_change_record` 
            MODIFY COLUMN `operate_user_id` bigint NULL COMMENT '操作人id' AFTER `operate_object`;
  - changeSet:
      id: dwy-business-36
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            MODIFY COLUMN `model_pic` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模特头图URI' AFTER `id`,
            MODIFY COLUMN `sex_dict` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '性别_字典值' AFTER `sex`,
            MODIFY COLUMN `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '模特类型(0:影响者,1:素人)' AFTER `sex_dict`,
            MODIFY COLUMN `type_dict` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模特类型_字典值' AFTER `type`,
            MODIFY COLUMN `age_group` tinyint(1) NOT NULL COMMENT '年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）' AFTER `type_dict`,
            MODIFY COLUMN `age_group_dict` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '年龄层_字典值' AFTER `age_group`,
            MODIFY COLUMN `birthday` date NULL DEFAULT NULL COMMENT '出生日期' AFTER `age_group_dict`,
            MODIFY COLUMN `age` int NULL DEFAULT NULL COMMENT '年龄' AFTER `birthday`,
            MODIFY COLUMN `nation_dict` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '国家_字典值' AFTER `nation`,
            MODIFY COLUMN `recipient` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收件人' AFTER `nation_dict`,
            MODIFY COLUMN `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市' AFTER `state`,
            MODIFY COLUMN `zipcode` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮编' AFTER `city`,
            MODIFY COLUMN `detail_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址' AFTER `zipcode`,
            MODIFY COLUMN `platform_dict` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台字典' AFTER `platform`,
            MODIFY COLUMN `acceptability` int NOT NULL COMMENT '待完成最高接受量' AFTER `cooperation`,
            MODIFY COLUMN `commission_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）' AFTER `acceptability`,
            MODIFY COLUMN `commission` decimal(12, 2) NOT NULL COMMENT '模特佣金' AFTER `commission_unit`,
            MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)' AFTER `commission`,
            MODIFY COLUMN `live_pic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生活场景照（FK:biz.resource.id 多个,隔开）' AFTER `status_explain`,
            MODIFY COLUMN `amazon_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '亚马逊案例视频（FK:model_video_resource.id 多个,隔开）' AFTER `live_pic`,
            MODIFY COLUMN `tiktok_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'tiktok案例视频（FK:model_video_resource.id 多个,隔开）' AFTER `amazon_video`,
            MODIFY COLUMN `source_from` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '来源' AFTER `after_sale_rate`;
  - changeSet:
      id: dwy-business-37
      author: dwy
      changes:
        - setColumnRemarks:
            tableName: model
            columnName: live_pic
            columnDataType: varchar(300)
      comment: 修改生活场景照字符长度
  - changeSet:
      id: dwy-business-38
      author: dwy
      changes:
      - addNotNullConstraint:
          tableName: model
          columnName: live_pic
          columnDataType: varchar(300)
  - changeSet:
      id: dwy-business-39
      author: dwy
      changes:
        - setColumnRemarks:
            tableName: model
            columnName: live_pic
            columnDataType: varchar(300)
            remarks: 生活场景照（FK:biz.resource.id 多个,隔开）
  - changeSet:
      id: dwy-business-40
      author: dwy
      changes:
        - addNotNullConstraint:
            tableName: model
            columnName: live_pic
            columnDataType: varchar(300)
        - setColumnRemarks:
            tableName: model
            columnName: live_pic
            columnDataType: varchar(300)
            remarks: 生活场景照（FK:biz.resource.id 多个,隔开）
  - changeSet:
      id: dwy-business-41
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            MODIFY COLUMN `live_pic` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生活场景照（FK:biz.resource.id 多个,隔开）' AFTER `status_explain`;
      comment: dwy-business-37 ~ dwy-business-41 目的为修改model.live_pic字符长度为300 但是总会出现设置了not null 备注消失，或者设置了备注 但是不会设置not null 最终用sql处理
  - changeSet:
      id: dwy-business-42
      author: dwy
      changes:
      - dropColumn:
          tableName: model
          columns:
            - column:
                name: source_from
  - changeSet:
      id: dwy-business-43
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `tag` 
            MODIFY COLUMN `parent_id` bigint NULL DEFAULT 0 COMMENT '父标签id' AFTER `id`,
            MODIFY COLUMN `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称' AFTER `parent_id`,
            MODIFY COLUMN `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签分类path' AFTER `english_name`,
            MODIFY COLUMN `category_id` bigint NOT NULL COMMENT '分类id' AFTER `path`;
  - changeSet:
      id: dwy-business-44
      author: dwy
      changes:
      - update:
          tableName: model_video_resource
          where: LENGTH(name) > 32
          columns:
            - column:
                name: name
                value: 'newName'
  - changeSet:
      id: dwy-business-45
      author: dwy
      changes:
      - update:
          tableName: model_video_resource
          where: LENGTH(pic_uri) > 64 OR pic_uri IS NULL OR pic_uri = ''
          columns:
            - column:
                name: pic_uri
                value: 'dev/84437074960b4d378fcce60eb49b799e.png'
  - changeSet:
      id: dwy-business-46
      author: dwy
      changes:
      - update:
          tableName: model_video_resource
          where: video_url IS NULL OR video_url = ''
          columns:
            - column:
                name: video_url
                value: 'https://amazon.com'
  - changeSet:
      id: dwy-business-47
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `model_video_resource` 
          MODIFY COLUMN `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '视频名称' AFTER `id`,
          MODIFY COLUMN `video_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '视频链接' AFTER `name`,
          MODIFY COLUMN `pic_uri` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '封面图片' AFTER `video_url`,
          MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `pic_uri`,
          MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;
  - changeSet:
      id: dwy-business-48
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `model_tag` 
          MODIFY COLUMN `model_id` bigint NOT NULL COMMENT '模特id' AFTER `id`,
          MODIFY COLUMN `dict_id` bigint NOT NULL COMMENT '标签id' AFTER `model_id`;
  - changeSet:
      id: dwy-business-49
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `model_person` 
          MODIFY COLUMN `model_id` bigint NOT NULL COMMENT '模特id' AFTER `id`,
          MODIFY COLUMN `user_id` bigint NOT NULL COMMENT '对接人id' AFTER `model_id`;
  - changeSet:
      id: dwy-business-50
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_account_collect_model` 
            CHANGE COLUMN `account_id` `biz_user_id` bigint NOT NULL COMMENT '商家端用户id FK:biz_user.id' AFTER `id`,
            MODIFY COLUMN `model_id` bigint NOT NULL COMMENT '模特id FK:model.id' AFTER `biz_user_id`;
      comment: business_account_collect_model 关联子账号id改为关联商家端用户id，biz_user_id和model_id补充非空限制
  - changeSet:
      id: dwy-business-51
      author: dwy
      changes:
      - addColumn:
          tableName: marketing_channel
          columns:
            - column:
                name: landing_form
                type: tinyint(1)
                defaultValue: 1
                remarks: 落地形式
                constraints:
                  nullable: false
                afterColumn: marketing_channel_name
  - changeSet:
      id: dwy-business-52
      author: dwy
      changes:
        - setColumnRemarks:
            tableName: marketing_channel
            columnName: landing_form
            columnDataType: tinyint(1)
            remarks: 落地形式（1:官网首页，2:添加企微客服）
  - changeSet:
      id: dwy-business-53
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `marketing_channel` 
          MODIFY COLUMN `landing_form` tinyint(1) NOT NULL DEFAULT 1 COMMENT '落地形式（1:官网首页，2:添加企微客服）' AFTER `marketing_channel_name`,
          MODIFY COLUMN `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '备注' AFTER `status`;
      comment: 落地形式改为非空，备注字段改为64字符 可以为空
  - changeSet:
      id: dwy-business-54
      author: dwy
      changes:
      - createIndex:
          tableName: marketing_channel
          indexName: uk_marketing_channel_name
          unique: true
          columns:
            - column:
                name: marketing_channel_name
      - createIndex:
          tableName: marketing_channel
          indexName: uk_dedicated_link_code
          unique: true
          columns:
            - column:
                name: dedicated_link_code
      - createIndex:
          tableName: marketing_channel
          indexName: uk_we_chat_url
          unique: true
          columns:
            - column:
                name: we_chat_url
  - changeSet:
      id: dwy-business-55
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `marketing_channel` 
          MODIFY COLUMN `we_chat_url` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '专属企微二维码地址' AFTER `dedicated_link_code`
      comment: 专属企微二维码地址非空限制去除
  - changeSet:
      id: dwy-business-56
      author: dwy
      changes:
        - addColumn:
            tableName: marketing_channel
            columns:
              - column:
                  name: update_id
                  type: bigint
                  constraints:
                    nullable: false
                  afterColumn: create_time
                  remarks: 更新人id
  - changeSet:
      id: dwy-business-57
      author: dwy
      changes:
        - addColumn:
            tableName: marketing_channel
            columns:
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  afterColumn: create_id
                  remarks: 更新人名称
  - changeSet:
      id: dwy-business-58
      author: dwy
      changes:
        - addColumn:
            tableName: marketing_channel
            columns:
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  afterColumn: create_by
                  remarks: 更新时间
  - changeSet:
      id: dwy-business-59
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `marketing_channel` 
          MODIFY COLUMN `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人' AFTER `create_id`,
          MODIFY COLUMN `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '更新人名称' AFTER `update_id`,
          MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by`;
      comment: 字段排序调整以及更新时间设置默认值
  - changeSet:
      id: dwy-business-60
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `marketing_channel` 
          MODIFY COLUMN `we_chat_url` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '专属企微二维码地址' AFTER `dedicated_link_code`;
      comment: 专属企微二维码地址非空限制 回调dwy-business-55
  - changeSet:
      id: dwy-business-61
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: sort
                  type: int(6)
                  constraints:
                    nullable: true
                  afterColumn: top_time
                  remarks: 排序值
                  defaultValue: 0
  - changeSet:
      id: dwy-business-62
      author: dwy
      changes:
        - sql: |
            ALTER TABLE model
            MODIFY COLUMN sort MEDIUMINT UNSIGNED DEFAULT '0' COMMENT '排序值';
  - changeSet:
      id: dwy-business-63
      author: dwy
      changes:
        - sql: |
            UPDATE model SET top_time = NULL WHERE `status` IN ( 1, 3 ) AND top_time IS NOT NULL
      comment: 暂停合作和取消合作的模特置顶时间置空
  - changeSet:
      id: dwy-business-64
      author: dwy
      changes:
        - createIndex:
            tableName: logistic_info
            indexName: idx_number_cur_time
            unique: false
            columns:
              - column:
                  name: number
              - column:
                  name: cur_time
  - changeSet:
      id: dwy-business-65
      author: dwy
      changes:
        - sql: |
            update page_config
            set user_perms = "page_config_tiktok" where name = "TikTok-精选案例页" and id = 2;
            update page_config
            set user_perms = "page_config_amazon" where name = "Amazon-精选案例页" and id = 3;
  - changeSet:
      id: dwy-business-66-1733973319
      author: dwy
      changes:
        - update:
            tableName: model
            where: after_sale_rate != 0.00
            columns:
              - column:
                  name: after_sale_rate
                  value: 0
      comment: 清空模特售后率
  - changeSet:
      id: dwy-business-67-1736909594
      author: dwy
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: settle_amount_currency
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  afterColumn: settle_amount
                  remarks: 最终结算金额（对应币种实付）
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: currency
                  type: tinyint(2)
                  constraints:
                    nullable: true
                  remarks: 币种（详见sys_dict_type.dict_type = sys_money_type）
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: pay_type
                  type: tinyint(2)
                  constraints:
                    nullable: true
                  remarks: 支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）
  - changeSet:
      id: dwy-business-68-1736909805
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_member_channel` 
            MODIFY COLUMN `currency` tinyint NULL DEFAULT NULL COMMENT '币种（详见sys_dict_type.dict_type = sys_money_type）' AFTER `settle_amount_currency`,
            MODIFY COLUMN `pay_type` tinyint NULL DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）' AFTER `currency`;
  - changeSet:
      id: dwy-business-69-1736910013
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_member_channel` 
            CHANGE COLUMN `settle_amount_currency` `real_pay_amount_currency` decimal(12, 2) NULL DEFAULT NULL COMMENT '最终结算金额（对应币种实付）' AFTER `real_pay_amount`,
            MODIFY COLUMN `currency` tinyint NULL DEFAULT NULL COMMENT '币种（详见sys_dict_type.dict_type = sys_money_type）' AFTER `real_pay_amount_currency`,
            MODIFY COLUMN `pay_type` tinyint NULL DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）' AFTER `currency`;
  - changeSet:
      id: dwy-business-70-1737018795
      author: dwy
      changes:
        - addColumn:
            tableName: business_member_validity_flow
            columns:
              - column:
                  name: real_pay_amount_currency
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 订单实付金额（对应币种实付）
                  afterColumn: member_package_type
  - changeSet:
      id: dwy-business-71-1737018949
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_member_validity_flow` 
            MODIFY COLUMN `currency` tinyint(1) NOT NULL DEFAULT 1 COMMENT '币种（详见sys_dict_type.dict_type = sys_money_type）' AFTER `real_pay_amount_currency`;
  - changeSet:
      id: dwy-business-72-1738981112
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: is_show
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否展示（1:展示,0:不展示）
                  afterColumn: sync_msg
  - changeSet:
      id: dwy-business-73-1738981593
      author: dwy
      changes:
        - dropColumn:
            tableName: model
            columns:
              - column:
                  name: is_show
  - changeSet:
      id: dwy-business-74-1738981597
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: is_show
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否展示（1:展示,0:不展示）
                  afterColumn: sync_msg
                  defaultValue: 1
  - changeSet:
      id: dwy-business-75-1738999157
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: about
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 模特简介
                  afterColumn: is_show
  - changeSet:
      id: dwy-business-72-1738984342
      author: dwy
      changes:
        - addColumn:
            tableName: logistic
            columns:
              - column:
                  name: latest_main_status
                  type: varchar(20)
                  constraints:
                    nullable: true
                  remarks: 物流最新主状态
                  afterColumn: number
  - changeSet:
      id: dwy-business-73-1738984392
      author: dwy
      changes:
        - addColumn:
            tableName: logistic
            columns:
              - column:
                  name: latest_sub_status
                  type: varchar(20)
                  constraints:
                    nullable: true
                  remarks: 物流最新子状态
                  afterColumn: latest_main_status
  - changeSet:
      id: dwy-order-76-1739166328
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `logistic` 
            MODIFY COLUMN `latest_sub_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '物流最新子状态' AFTER `latest_main_status`;
      comment: latest_sub_status字段长度修改为50
  - changeSet:
      id: dwy-order-77-1739273130678
      author: dwy
      changes:
        - createTable:
            tableName: business_callback
            isNotExists: true
            remarks: 商家_回访表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 商家ID (FK:business.id)
              - column:
                  name: mark_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 标记时间
              - column:
                  name: write_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 记录时间
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 修改时间
        - createTable:
            tableName: business_callback_event
            isNotExists: true
            remarks: 商家_回访_事件表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: callback_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 回访ID (FK:business_callback.id)
              - column:
                  name: callback_event
                  type: tinyint(2)
                  constraints:
                    nullable: false
                  remarks: 回访事件
              - column:
                  name: callback_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 最晚回访时间
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 修改时间
        - createTable:
            tableName: business_callback_record
            isNotExists: true
            remarks: 商家_回访_记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: callback_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 回访ID (FK:business_callback.id)
              - column:
                  name: account_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 回访账号ID
              - column:
                  name: feedback_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 反馈类型
              - column:
                  name: callback_content
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 回访内容
              - column:
                  name: resource_id
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 回访图片（FK:biz.resource.id 多个,隔开）
              - column:
                  name: write_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间
              - column:
                  name: write_by
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 记录人
              - column:
                  name: write_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 记录人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 修改时间
        - createTable:
            tableName: business_callback_record_event
            isNotExists: true
            remarks: 商家_回访_记录_事件表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: callback_record_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 回访记录ID (FK:business_callback_record.id)
              - column:
                  name: callback_event
                  type: tinyint(2)
                  constraints:
                    nullable: false
                  remarks: 回访事件
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 修改时间
  - changeSet:
      id: dwy-order-78-1739274588690
      author: dwy
      changes:
        - addColumn:
            tableName: business_callback
            columns:
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 回访状态（1:待回访,2:回访中,3:已回访）
                  afterColumn: business_id
                  defaultValue: 1
              - column:
                  name: is_valid
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否有效（1:有效,0:失效）
                  afterColumn: business_id
  - changeSet:
      id: dwy-order-78-1739275189682
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_callback` 
            MODIFY COLUMN `is_valid` tinyint(1) NULL DEFAULT 1 COMMENT '是否有效（1:有效,0:失效）' AFTER `status`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;
            ALTER TABLE `business_callback_event` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;
            ALTER TABLE `business_callback_record` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;
            ALTER TABLE `business_callback_record_event` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-79-1739322941974
      author: dwy
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: recent_order_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 最近排单时间
                  afterColumn: is_exist_recent_order
  - changeSet:
      id: dwy-order-80-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_callback_record` 
            MODIFY COLUMN `feedback_type` varchar(10) NOT NULL COMMENT '反馈类型' AFTER `account_id`;
      comment: feedback_type改为varchar
  - changeSet:
      id: dwy-order-81-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_callback_record` 
            CHANGE COLUMN `callback_id` `business_id` bigint NOT NULL COMMENT '商家ID (FK:business.id)' AFTER `id`;
      comment: callback_id改为business_id
  - changeSet:
      id: dwy-order-82-*************
      author: dwy
      changes:
        - addColumn:
            tableName: business_callback_record
            columns:
              - column:
                  name: callback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回访ID（FK:business_callback.id）
                  afterColumn: business_id
  - changeSet:
      id: dwy-order-83-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_callback_record` 
            MODIFY COLUMN `callback_id` bigint NOT NULL COMMENT '回访ID（FK:business_callback.id）' AFTER `business_id`;
  - changeSet:
      id: dwy-order-84-*************
      author: dwy
      changes:
        - addColumn:
            tableName: business_callback_record
            columns:
              - column:
                  name: account_is_owner_account
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否主账号（0-否， 1-是）
                  afterColumn: account_id
  - changeSet:
      id: dwy-order-85-*************
      author: dwy
      changes:
        - addColumn:
            tableName: business_callback_record
            columns:
              - column:
                  name: account_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 员工名称
                  afterColumn: account_is_owner_account
  - changeSet:
      id: dwy-order-86-*************
      author: dwy
      changes:
        - addColumn:
            tableName: business_callback_record
            columns:
              - column:
                  name: account_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 员工微信名称
                  afterColumn: account_name
  - changeSet:
      id: dwy-order-87-*************
      author: dwy
      changes:
        - addColumn:
            tableName: business_callback_record
            columns:
              - column:
                  name: account_biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 员工BizUserID
                  afterColumn: callback_id
  - changeSet:
      id: dwy-order-88-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_callback_record` 
            MODIFY COLUMN `account_biz_user_id` bigint NOT NULL COMMENT '回访账号BizUserID' AFTER `callback_id`,
            MODIFY COLUMN `account_is_owner_account` tinyint(1) NOT NULL COMMENT '回访账号是否主账号（0-否， 1-是）' AFTER `account_id`,
            MODIFY COLUMN `account_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回访账号员工名称' AFTER `account_is_owner_account`,
            MODIFY COLUMN `account_nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回访账号员工微信名称' AFTER `account_name`;
      comment: 字段注释修改
  - changeSet:
      id: dwy-order-89-*************
      author: dwy
      changes:
        - dropColumn:
            tableName: business_callback
            columns:
              - column:
                  name: is_valid
  - changeSet:
      id: dwy-order-90-*************
      author: dwy
      changes:
        - dropTable:
            tableName: business_callback_record_event
  - changeSet:
      id: dwy-order-91-*************
      author: dwy
      changes:
        - addColumn:
            tableName: distribution_channel
            columns:
              - column:
                  name: poster_name
                  type: varchar(12)
                  constraints:
                    nullable: false
                  remarks: 海报名称
                  afterColumn: channel_name
  - changeSet:
      id: dwy-order-92-*************
      author: dwy
      changes:
        - sql: |
            update distribution_channel set poster_name = channel_name
      comment: 初始化poster_name字段
  - changeSet:
      id: dwy-order-93-1741916894548
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: status_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 状态变更时间
                  afterColumn: status
  - changeSet:
      id: dwy-order-93-1739951953880
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `amazon_goods_pic` 
            MODIFY COLUMN `product_name` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品名称' AFTER `goods_id`;
  - changeSet:
      id: dwy-order-94-*************
      author: dwy
      changes:
        - addColumn:
            tableName: model_video_resource
            columns:
              - column:
                  name: sort
                  type: smallint(2)
                  constraints:
                    nullable: false
                  remarks: 排序（倒序）
                  afterColumn: pic_uri
  - changeSet:
      id: dwy-order-95-*************
      author: dwy
      changes:
        - addColumn:
            tableName: business_account
            columns:
              - column:
                  name: owner_account_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 主账号ID
              - column:
                  name: owner_account_biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 主账号用户ID
  - changeSet:
      id: dwy-order-96-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_account` 
            MODIFY COLUMN `owner_account_id` bigint NOT NULL COMMENT '主账号ID' AFTER `is_owner_account`,
            MODIFY COLUMN `owner_account_biz_user_id` bigint NOT NULL COMMENT '主账号用户ID' AFTER `owner_account_id`,
            MODIFY COLUMN `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '名称' AFTER `owner_account_biz_user_id`;
      comment: 修改排序
  - changeSet:
      id: dwy-order-97-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_account` 
            CHANGE COLUMN `owner_account_id` `owner_account` varchar(20) NOT NULL COMMENT '主账号' AFTER `is_owner_account`;
      comment: 修改字段名称、类型
  - changeSet:
      id: dwy-order-94-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `distribution_channel` 
            MODIFY COLUMN `poster_name` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '海报名称' AFTER `channel_name`;
      comment: 删除非空限制
  - changeSet:
      id: dwy-business-95-*************
      author: dwy
      changes:
        - addColumn:
            tableName: business_balance_prepay
            columns:
              - column:
                  name: pay_type_detail
                  type: tinyint(2)
                  constraints:
                    nullable: true
                  remarks: 支付方式明细（1：其他平台/银行支付，2：万里汇）
                  afterColumn: pay_type
  - changeSet:
      id: dwy-business-96-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_balance_prepay` 
            MODIFY COLUMN `pay_type_detail` tinyint NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
      comment: 注释修改
  - changeSet:
      id: dwy-business-97-1745812544681
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `business_balance_prepay` 
            MODIFY COLUMN `pay_type_detail` SMALLINT NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
      comment: 修改数据类型
  - changeSet:
      id: dwy-order-99-1745892744449
      author: dwy
      changes:
        - sql: |
            UPDATE business_balance_prepay 
            SET pay_type_detail = 701 
            WHERE
            	pay_type_detail IS NULL AND ( pay_type = 7 OR pay_type = 17 );
      comment: 初始化pay_type_detail数据
  - changeSet:
      id: dwy-order-100-1746610870872
      author: dwy
      changes:
        - createTable:
            tableName: model_data_statistics_day
            isNotExists: true
            remarks: 模特数据统计_每日记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: write_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间
              - column:
                  name: basic_data_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 模特基本数据JSON
              - column:
                  name: type_analysis_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 模特类型分析JSON
              - column:
                  name: match_count_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 模特匹配次数JSON
              - column:
                  name: model_new_number
                  type: smallint(3)
                  constraints:
                    nullable: true
                  remarks: 模特新增数量
              - column:
                  name: model_oust_number
                  type: smallint(3)
                  constraints:
                    nullable: true
                  remarks: 模特淘汰数量
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: model_data_statistics_month
            isNotExists: true
            remarks: 模特数据统计_每月记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: write_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间
              - column:
                  name: new_model_analysis_added_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 模特每月新增模特分析JSON
              - column:
                  name: oust_model_analysis_added_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 模特每月淘汰模特分析JSON
              - column:
                  name: ranking_list_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 模特排行榜JSON
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-101-1746753491504
      author: dwy
      changes:
        - dropColumn:
            tableName: model_data_statistics_day
            columns:
              - column:
                  name: basic_data_json
  - changeSet:
      id: dwy-order-102-1746779409127
      author: dwy
      changes:
        - addColumn:
            tableName: model_data_statistics_month
            columns:
              - column:
                  name: model_new_number
                  type: smallint(3)
                  constraints:
                    nullable: true
                  remarks: 模特新增数量
                  afterColumn: ranking_list_json
  - changeSet:
      id: dwy-order-103-1746779513129
      author: dwy
      changes:
        - addColumn:
            tableName: model_data_statistics_month
            columns:
              - column:
                  name: model_oust_number
                  type: smallint(3)
                  constraints:
                    nullable: true
                  remarks: 模特淘汰数量
                  afterColumn: model_new_number
  - changeSet:
      id: dwy-order-104-1747028684571
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_data_statistics_day` 
            DROP COLUMN `type_analysis_json`,
            DROP COLUMN `match_count_json`,
            MODIFY COLUMN `write_time` datetime NOT NULL COMMENT '记录时间（截止记录该时间前的数据）' AFTER `id`;
            ALTER TABLE `model_data_statistics_month` 
            MODIFY COLUMN `write_time` datetime NOT NULL COMMENT '记录时间（截止记录该时间前的数据）' AFTER `id`;
      comment: 删除无用字段以及注释修改
  - changeSet:
      id: dwy-order-105-1747127909030
      author: dwy
      changes:
        - createTable:
            tableName: model_added_snap_shoot
            isNotExists: true
            remarks: 模特新增快照表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: model_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特ID（FK：model.id）
              - column:
                  name: cooperation
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 合作深度(0:一般模特,1:优质模特,2:中等模特)
              - column:
                  name: commission_unit
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
              - column:
                  name: commission
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 模特佣金
              - column:
                  name: extra_data
                  type: json
                  constraints:
                    nullable: false
                  remarks: 模特对象JSON
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: model_oust_snap_shoot
            isNotExists: true
            remarks: 模特淘汰快照表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: model_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特ID（FK：model.id）
              - column:
                  name: cooperation
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 合作深度(0:一般模特,1:优质模特,2:中等模特)
              - column:
                  name: commission_unit
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
              - column:
                  name: commission
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 模特佣金
              - column:
                  name: extra_data
                  type: json
                  constraints:
                    nullable: false
                  remarks: 模特对象JSON
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
        - createIndex:
            tableName: model_added_snap_shoot
            indexName: uk_model_id
            unique: true
            columns:
              - column:
                  name: model_id
        - createIndex:
            tableName: model_oust_snap_shoot
            indexName: idx_model_id
            unique: false
            columns:
              - column:
                  name: model_id
  - changeSet:
      id: dwy-order-106-1747128744028
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_oust_snap_shoot` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
      comment: 设置更新时间默认值
  - changeSet:
      id: dwy-order-107-1747188766225
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_data_statistics_month` 
            CHANGE COLUMN `new_model_analysis_added_json` `new_model_analysis_json` json NULL COMMENT '模特每月新增模特分析JSON' AFTER `write_time`,
            CHANGE COLUMN `oust_model_analysis_added_json` `oust_model_analysis_json` json NULL COMMENT '模特每月淘汰模特分析JSON' AFTER `new_model_analysis_json`;
      comment: 字段更名
  - changeSet:
      id: dwy-order-108-1747189073375
      author: dwy
      changes:
        - addColumn:
            tableName: model_data_statistics_day
            columns:
              - column:
                  name: write_time_begin
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-开始
                  afterColumn: id
        - addColumn:
            tableName: model_data_statistics_month
            columns:
              - column:
                  name: write_time_begin
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-开始
                  afterColumn: id
  - changeSet:
      id: dwy-order-109-1747189073375
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_data_statistics_day` 
            CHANGE COLUMN `write_time` `write_time_end` datetime NOT NULL COMMENT '记录时间-结束' AFTER `write_time_begin`;
            ALTER TABLE `model_data_statistics_month` 
            CHANGE COLUMN `write_time` `write_time_end` datetime NOT NULL COMMENT '记录时间-结束' AFTER `write_time_begin`;
      comment: 字段更名
  - changeSet:
      id: dwy-order-110-1747213761311
      author: dwy
      changes:
        - addColumn:
            tableName: model_change_record
            columns:
              - column:
                  name: state_before_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 变更状态前模特状态(0:正常合作,1:暂停合作,2:行程中,3:取消合作)
                  afterColumn: operate_explain
  - changeSet:
      id: dwy-order-100-1745998256312
      author: dwy
      changes:
        - addColumn:
            tableName: model_tag
            columns:
              - column:
                  name: dict_name
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 标签名称
                  afterColumn: dict_id
  - changeSet:
      id: dwy-order-101-1745998352316
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_tag` 
            MODIFY COLUMN `dict_id` bigint NULL COMMENT '标签id' AFTER `model_id`;
      comment: 删除非空
  - changeSet:
      id: dwy-order-102-1746003307624
      author: dwy
      changes:
        - addColumn:
            tableName: model_tag
            columns:
              - column:
                  name: category_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 标签分类ID
                  afterColumn: dict_name
  - changeSet:
      id: dwy-order-103-1746003399626
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model_tag` 
            CHANGE COLUMN `category_id` `dict_category_id` bigint NULL DEFAULT NULL COMMENT '标签分类ID' AFTER `dict_name`;
      comment: 修改字段名称
  - changeSet:
      id: dwy-order-104-1746007470758
      author: dwy
      changes:
        - addColumn:
            tableName: model_video_resource
            columns:
              - column:
                  name: model_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特ID （FK：model.id）
                  afterColumn: id
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 类型（1：亚马逊案例视频，2：TikTok案例视频）
                  afterColumn: name
  - changeSet:
      id: dwy-order-105-1750661755725
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: have_snail_pic
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 有蜗牛照（1：有，0：没有）
                  defaultValue: 0
                  afterColumn: about
              - column:
                  name: developer
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 开发人
                  defaultValue: 0
                  afterColumn: about
  - changeSet:
      id: dwy-order-106-1750661847663
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            MODIFY COLUMN `have_snail_pic` tinyint(1) NOT NULL DEFAULT 0 COMMENT '有蜗牛照（1：有，0：没有）' AFTER `developer`,
            MODIFY COLUMN `create_by` bigint NULL DEFAULT NULL COMMENT '创建人' AFTER `have_snail_pic`,
            MODIFY COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_by`,
            MODIFY COLUMN `update_by` bigint NULL DEFAULT NULL COMMENT '更新人' AFTER `create_time`,
            MODIFY COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by`;
      comment: 表字段排序
  - changeSet:
      id: dwy-order-107-1750662106663
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            CHANGE COLUMN `developer` `developer_id` bigint NOT NULL DEFAULT 0 COMMENT '开发人ID' AFTER `about`;
      comment: 字段更名
  - changeSet:
      id: dwy-order-108-1750663579672
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            DROP COLUMN `have_snail_pic`;
  - changeSet:
      id: dwy-order-109-1750663981664
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: have_snail_pic
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 有蜗牛照（1：有，0：没有）
                  afterColumn: about
  - changeSet:
      id: dwy-order-110-1751621876318
      author: dwy
      changes:
        - addColumn:
            tableName: model_tag
            columns:
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  afterColumn: dict_category_id
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-111-1751977788543
      author: dwy
      changes:
        - createTable:
            tableName: model_data_table_remark
            isNotExists: true
            remarks: 模特数据表备注表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: model_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特ID（FK：model.id）
              - column:
                  name: remark
                  type: varchar(200)
                  constraints:
                    nullable: false
                  remarks: 备注内容
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
  - changeSet:
      id: dwy-order-112-1751978038543
      author: dwy
      changes:
        - createIndex:
            tableName: model_data_table_remark
            indexName: idx_model_id
            unique: false
            columns:
              - column:
                  name: model_id
  - changeSet:
      id: dwy-order-113-1752198472703
      author: dwy
      changes:
        - sql: |
            ALTER TABLE model 
              ADD COLUMN status_sort  TINYINT AS (
                CASE STATUS WHEN 0 THEN 1 WHEN 2 THEN 2 WHEN 1 THEN 3 WHEN 3 THEN 4 END
              ) STORED,
              ADD COLUMN cooperation_sort INT AS (
                CASE 
                  WHEN top_time IS NOT NULL THEN 400000 + sort*10 +
                       (CASE cooperation WHEN 1 THEN 3 WHEN 2 THEN 2 WHEN 0 THEN 1 ELSE 0 END)
                  ELSE 100000 * (CASE cooperation WHEN 1 THEN 3 WHEN 2 THEN 2 WHEN 0 THEN 1 ELSE 0 END)
                       + sort
                END
              ) STORED;
  - changeSet:
      id: dwy-order-114-1752198667753
      author: dwy
      changes:
        - createIndex:
            tableName: model
            indexName: idx_ss_cs_ut_id
            unique: false
            columns:
              - column:
                  name: status_sort
              - column:
                  name: cooperation_sort
              - column:
                  name: update_time
              - column:
                  name: id
  - changeSet:
      id: dwy-order-116-1752219393766
      author: dwy
      changes:
        - createIndex:
            tableName: model
            indexName: idx_family_id
            unique: false
            columns:
              - column:
                  name: family_id
        - createIndex:
            tableName: model_tag
            indexName: idx_model_id
            unique: false
            columns:
              - column:
                  name: model_id
        - createIndex:
            tableName: model_tag
            indexName: idx_dict_id
            unique: false
            columns:
              - column:
                  name: dict_id
        - createIndex:
            tableName: model_video_resource
            indexName: idx_model_id
            unique: false
            columns:
              - column:
                  name: model_id
        - createIndex:
            tableName: user_model_blacklist
            indexName: idx_model_id
            unique: false
            columns:
              - column:
                  name: model_id
  - changeSet:
      id: dwy-order-117-*************
      author: dwy
      changes:
        - createIndex:
            tableName: business_account_collect_model
            indexName: idx_model_id
            unique: false
            columns:
              - column:
                  name: model_id
  - changeSet:
      id: dwy-order-118-*************
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: video_last_update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 视频案例最新更新时间
                  afterColumn: cooperation_sort
  - changeSet:
      id: dwy-order-119-*************
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: tag_last_update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 标签最新更新时间
                  afterColumn: video_last_update_time
  - changeSet:
      id: dwy-order-120-1752229170848
      author: dwy
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: category_last_update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 品类最新更新时间
                  afterColumn: tag_last_update_time
  - changeSet:
      id: dwy-order-115-1752198816766
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `model` 
            MODIFY COLUMN `status_sort` tinyint GENERATED ALWAYS AS ((case `status` when 0 then 1 when 2 then 2 when 1 then 3 when 3 then 4 end)) STORED NULL AFTER `developer_id`,
            MODIFY COLUMN `cooperation_sort` int GENERATED ALWAYS AS ((case when (`top_time` is not null) then ((400000 + (`sort` * 10)) + (case `cooperation` when 1 then 3 when 2 then 2 when 0 then 1 else 0 end)) else ((100000 * (case `cooperation` when 1 then 3 when 2 then 2 when 0 then 1 else 0 end)) + `sort`) end)) STORED NULL AFTER `status_sort`;
      comment: 更改排序