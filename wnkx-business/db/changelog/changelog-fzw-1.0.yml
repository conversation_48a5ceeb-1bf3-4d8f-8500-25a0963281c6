databaseChangeLog:
  - logicalFilePath: 'changelog-fzw-1.0.yml'
  - changeSet:
      id: 1
      author: fzw
      changes:
        - sql:
            sql: show databases
            comment: initial change
  - changeSet:
      id: 1722999172
      author: fzw
      changes:
        - setColumnRemarks:
            columnName: platform_dict
            remarks: 平台字典
            columnDataType: varchar(40)
            tableName: model
            comment: 修改model表platform_dict字段长度与备注
  - changeSet:
      id: 1727229516
      author: fzw
      changes:
        - createTable:
            tableName: we_chat_contact_config
            columns:
              - column:
                  name: id
                  type: BIGINT(20)
                  autoIncrement: true
                  constraints:
                    nullable: false
                    primaryKey: true
                  remarks: id主键
              - column:
                  name: config_id
                  type: varchar(32)
                  constraints:
                    nullable: false
                    unique: true
                  remarks: 配置id
              - column:
                  name: qr_code
                  type: varchar(255)
                  constraints:
                    nullable: false
                  remarks: 二维码
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
                  remarks: 更新时间
            remarks: 客户联系联系我二维码缓存表
  - changeSet:
      id: 1728887500
      author: fzw
      changes:
        - dropNotNullConstraint:
            columnName: live_pic
            columnDataType: varchar(300)
            tableName: model
      comment: 删除model表live_pic字段非空约束，模特生活照现在可以为空
  - changeSet:
      id: 1731393237
      author: fzw
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: business_wechat_url
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 商家企业微信专属url
      comment: 添加business表business_wechat_url字段,用于存储商家与企业微信的url

  - changeSet:
      id: 1731893653
      author: fzw
      changes:
        - addColumn:
            tableName: business_balance_audit_flow
            columns:
              - column:
                  name: withdraw_number
                  type: varchar(20)
                  constraints:
                    nullable: true
                  remarks: 提现编号
        - addColumn:
            tableName: business_balance_audit_flow
            columns:
              - column:
                  name: apply_remark
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 提现申请备注
        - addColumn:
            tableName: business_balance_audit_flow
            columns:
              - column:
                  name: withdraw_way
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 提现类型（1-微信，2-支付宝，3-银行卡，4-境外汇款，5-其他）
  - changeSet:
      id: 1731894842
      author: fzw
      changes:
        - addColumn:
            tableName: business_balance_audit_flow
            columns:
              - column:
                  name: notify_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 提现通知状态 0-未通知 1-已通知
        - addColumn:
            tableName: business_balance_audit_flow
            columns:
              - column:
                  name: notify_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 通知时间
  - changeSet:
      id: 1732611594
      author: fzw
      changes:
        - modifyDataType:
            tableName: model_change_record
            columnName: operate_explain
            newDataType: varchar(1000)
  - changeSet:
      id: 1732588795
      author: fzw
      changes:
        - modifyDataType:
            tableName: model
            columnName: status_explain
            newDataType: varchar(1000)
      comment: '修改模型状态说明字段长度为1000字'
  - changeSet:
      id: 1732589949
      author: fzw
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: cancel_cooperation_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 取消合作类型(0-我们取消,1-模特取消)
              - column:
                  name: cancel_cooperation_sub_type
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 取消合作子类型(关联字典id)
      comment: '修改模型状态说明字段长度为1000字'
  - changeSet:
      id: 1732605428
      author: fzw
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: phone
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 手机号
      comment: '添加模特手机号字段'
  - changeSet:
      id: 1732693033
      author: fzw
      changes:
        - addColumn:
            tableName: business
            columns:
              - column:
                  name: phone_visible
                  type: tinyint(1)
                  constraints:
                      nullable: false
                  defaultValueNumeric: 0
                  remarks: 手机号是否可见(0-不可见,1-可见)
      comment: '添加商家手机号是否可见字段'
  - changeSet:
      id: 1732781138
      author: fzw
      changes:
        - modifyDataType:
            columnName: carrier_homepage
            newDataType: varchar(300)
            tableName: logistic
      comment: 修改logistic表carrier_homepage字段长度
  - changeSet:
      id: 1733119058
      author: fzw
      changes:
        - createTable:
            tableName: distribution_channel_activity
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: activity_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 活动名称
              - column:
                  name: discount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 折扣
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 活动状态
              - column:
                  name: start_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 开始时间
              - column:
                  name: end_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 结束时间
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: true
            remarks: '渠道活动表'
        - createTable:
            tableName: distribution_channel_activity_info
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: activity_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 活动id
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 渠道id
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                  remarks: 修改时间
                  constraints:
                    nullable: true
            remarks: '渠道活动关联表'

  - changeSet:
      id: 1733209832
      author: fzw
      changes:
        - addColumn:
            tableName: distribution_channel_activity
            columns:
              - column:
                    name: type
                    type: tinyint(1)
                    constraints:
                        nullable: false
                    defaultValueNumeric: 0
                    remarks: 类型(0-全部生效,1-部分生效,2-部分不生效)
  - changeSet:
      id: 1733728440
      author: fzw
      changes:
        - sql:
            ALTER TABLE logistic MODIFY COLUMN `number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物流单号';
      comment: 修改logistic表物流单号字段长度为100
  - changeSet:
      id: 1733729410
      author: fzw
      changes:
        - sql:
            ALTER TABLE logistic_info MODIFY COLUMN `number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物流单号';
      comment: 修改logistic表物流单号字段长度为100
  - changeSet:
      id: 1733999302
      author: fzw
      changes:
        - sql:
            ALTER TABLE model MODIFY COLUMN amazon_video varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '亚马逊案例视频（FK:model_video_resource.id 多个,隔开）';
      comment: 修改model表amazon_video字段长度为500
  - changeSet:
      id: **********
      author: fzw
      changes:
        - sql:
            ALTER TABLE model MODIFY COLUMN tiktok_video varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'tiktok案例视频（FK:model_video_resource.id 多个,隔开）';
      comment: 修改model表tiktok_video字段长度为500
  - changeSet:
      id: **********
      author: fzw
      changes:
        - createIndex:
            indexName: idx_biz_user_id_model_id
            tableName: business_account_collect_model
            unique: true
            columns:
              - column:
                  name: biz_user_id
              - column:
                  name: model_id
      comment: '创建business_account_collect_model表索引'
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: we_chat_external_user
            columns:
              - column:
                  name: connect_user_id
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 企业微信外部人员id
              - column:
                  name: connect_user_name
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 企业微信外部人员姓名
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: biz_user
            columns:
              - column:
                  name: connect_user_name
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 企业微信外部人员姓名
  - changeSet:
      id: **********
      author: fzw
      changes:
        - createIndex:
            indexName: business_account_biz_user_id_IDX
            tableName: business_account
            unique: false
            columns:
              - column:
                  name: biz_user_id
              - column:
                  name: business_id
      comment: '创建business_account表索引'
  - changeSet:
      id: **********
      author: fzw
      changes:
        - createTable:
            tableName: distribution_channel_visit_flow
            remarks: 分销渠道访问流水数据
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: channel_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 分销id（distribution_channel.id）
              - column:
                  name: unique_visitor
                  type: int(32)
                  constraints:
                    nullable: false
                  remarks: 独立访客
                  defaultValueNumeric: 0
              - column:
                  name: page_view
                  type: int(32)
                  constraints:
                    nullable: false
                  remarks: 访问量
                  defaultValueNumeric: 0
              - column:
                  name: bounce_rate
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 跳出率
                  defaultValueComputed: 0.00
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 采集时间
  - changeSet:
      id: 1741153257
      author: fzw
      changes:
        - createTable:
            tableName: we_chat_contact_user_config
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: contact_user_name
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: 对外联系人姓名
              - column:
                  name: contact_user_id
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: 对外联系人Id
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 修改时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            remarks: 对外联系人配置表
        - createTable:
            tableName: we_chat_contact_user_config_info
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: url_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: url类型(1-Sys-Website-1 , 2-Sys-Vip-1)
              - column:
                  name: config_id
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 配置id
              - column:
                  name: config_url
                  type: varchar(500)
                  constraints:
                    nullable: false
                  remarks: 配置url
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 修改时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            remarks: 对外联系人详细配置表
      comment: '微信对外联系人配置'
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: model_account
            columns:
              - column:
                  name: login_account
                  afterColumn: account
                  type: varchar(20)
                  remarks: 登录账户
        - createIndex:
            indexName: model_account_login_account_IDX
            tableName: login_account
            unique: true
            columns:
              - column:
                  name: login_account
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: amazon_goods_pic
            columns:
              - column:
                  name: product_name
                  type: varchar(200)
                  afterColumn: goods_id
                  remarks: 产品名称
              - column:
                  name: spec_info
                  type: varchar(5000)
                  afterColumn: object_key
                  remarks: 卖点html
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: amazon_goods_pic
            columns:
              - column:
                  name: product_chinese_name
                  type: varchar(400)
                  afterColumn: product_name
                  remarks: 产品中文名称
  - changeSet:
      id: **********
      author: fzw
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: cooperation_score
                  type: decimal(2,1)
                  constraints:
                    nullable: false
                  defaultValue: 0.0
                  remarks: 模特评分 (0.0-10.0)
                  afterColumn: cooperation