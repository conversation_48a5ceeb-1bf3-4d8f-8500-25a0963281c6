databaseChangeLog:
  - logicalFilePath: 'changelog-xjf-1.0.yml'
  - changeSet:
      id: xjf-1
      author: xjf
      changes:
        - addColumn:
            tableName: model
            columns:
              - column:
                  name: sync_msg
                  type: varchar(120)
                  constraints:
                    nullable: true
                  remarks: 同步提示语
  - changeSet:
      id: xjf-2
      author: xjf
      changes:
        - createTable:
            tableName: business_account_rebind_log
            remarks: 子账号日志表
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: business_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 商家id
              - column:
                  name: biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 用户id
              - column:
                  name: apply_id
                  type: bigint
                  defaultValue: null
                  remarks: 申请表id
              - column:
                  name: name
                  type: varchar(20)
                  remarks: 名称
                  constraints:
                    nullable: true
              - column:
                  name: nick_name
                  type: varchar(20)
                  remarks: 微信名称
                  constraints:
                    nullable: true
              - column:
                  name: phone
                  type: char(11)
                  remarks: 手机号
                  constraints:
                    nullable: true
              - column:
                  name: connect_user_name
                  type: varchar(64)
                  remarks: 售前
                  constraints:
                    nullable: true
              - column:
                  name: create_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 申请加入时间
                  constraints:
                    nullable: true
              - column:
                  name: audit_time
                  type: datetime
                  defaultValue: null
                  remarks: 审核时间
                  constraints:
                    nullable: true
              - column:
                  name: status
                  type: tinyint(1)
                  defaultValue: null
                  remarks: "0正常，1禁用，2删除，3解绑"
                  constraints:
                    nullable: true
              - column:
                  name: account
                  type: varchar(50)
                  remarks: 主账号
                  constraints:
                    nullable: true
              - column:
                  name: audit_status
                  type: tinyint(1)
                  defaultValue: null
                  remarks: 审批状态
                  constraints:
                    nullable: true
              - column:
                  name: update_time
                  type: datetime
                  defaultValueComputed: CURRENT_TIMESTAMP
                  remarks: 更新时间
                  constraints:
                    nullable: true
  - changeSet:
        id: xjf-3
        author: xjf
        changes:
          - modifyDataType:
              tableName: business_account_rebind_log
              columnName: name
              newDataType: varchar(50)
          - modifyDataType:
              tableName: business_account_rebind_log
              columnName: nick_name
              newDataType: varchar(50)
  - changeSet:
      id: xjf-4-**********
      author: xjf
      changes:
        - addColumn:
            tableName: biz_user_channel
            columns:
              - column:
                  name: business_id
                  type: bigint
                  constraints:
                    nullable: true
                  remarks: 商家id
  - changeSet:
      id: xjf-5-**********
      author: xjf
      changes:
        - modifyDataType:
            tableName: business_balance_prepay
            columnName: apply_remark
            newDataType: varchar(1000)
        - modifyDataType:
            tableName: business_balance_prepay
            columnName: remark
            newDataType: varchar(1000)
  - changeSet:
      id: xjf-**********
      author: xjf
      changes:
        - modifyDataType:
            tableName: business_balance_audit_flow
            columnName: apply_remark
            newDataType: varchar(1000)
        - modifyDataType:
            tableName: business_balance_audit_flow
            columnName: remark
            newDataType: varchar(1000)
  - changeSet:
      id: xjf-**********
      author: xjf
      changes:
        - sql:
            sql: |
              UPDATE biz_user
              SET status = 1
              WHERE id IN (
                   SELECT ba.biz_user_id
                   FROM business b
                   JOIN business_account ba ON b.id = ba.business_id
                   WHERE (b.status = 1 AND ba.is_owner_account = 1) OR (ba.`status` = 1) and biz_user_id is not null
              );
  - changeSet:
      id: xjf-**********
      author: xjf
      changes:
        - sql:
            sql: |
              ALTER TABLE tag MODIFY COLUMN `name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称';