package com.wnkx.biz.model.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.wnkx.biz.model.mapper.ModelPersonMapper;
import com.wnkx.biz.model.service.IModelPersonService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 模特对接人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
@RequiredArgsConstructor
public class ModelPersonServiceImpl extends ServiceImpl<ModelPersonMapper, ModelPerson> implements IModelPersonService {
    private final RemoteService remoteService;

    /**
     * 获取英文部客服 新增模特数
     */
    @Override
    public List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedModelCounts(String date) {
        return baseMapper.getEnglishCustomerServiceAddedModelCounts(date);
    }

    /**
     * 通过运营id获取模特
     */
    @Override
    public List<ModelPerson> selectListByUserIds(Collection<Long> userIds) {
        return baseMapper.selectListByUserIds(userIds);
    }

    /**
     * 通过模特id删除关联人员表
     */
    @Override
    public void removeByModelId(List<Long> modelId) {
        baseMapper.removeByModelId(modelId);
    }

    /**
     * 模特列表-获取关联人员下拉框（运营端）
     */
    @Override
    public List<UserVO> modelPersonsSelect(String keyword) {
        Set<Long> userIds = baseMapper.getUserId();
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(userIds);
        dto.setUserName(keyword);
        return remoteService.customerServiceSelect(dto);
    }


    /**
     * 通过模特id获取模特关联运营
     */
    @Override
    public List<ModelPerson> selectListByModelId(Collection<Long> modelId) {
        if (CollUtil.isEmpty(modelId)) return new ArrayList<>();
        return baseMapper.selectListByModelId(modelId);
    }
}
