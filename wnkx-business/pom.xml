<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi</artifactId>
        <version>3.5.0</version>
    </parent>

    <groupId>com.wnkx</groupId>
    <artifactId>wnkx-business</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>biz-center</module>
        <module>biz-job</module>
        <module>biz-center-core</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <db.name>biz-center</db.name>
        <db.ip>**********</db.ip>
        <db.port>4000</db.port>
    </properties>

    <profiles>
        <profile>
            <id>liquibase</id>
            <build>
                <resources>
                    <resource>
                        <directory>db</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
                <plugins>
                    <plugin>
                        <groupId>org.liquibase</groupId>
                        <artifactId>liquibase-maven-plugin</artifactId>
                        <version>4.17.0</version>
                        <inherited>false</inherited>
                        <configuration>
                            <propertyFile>liquibase.properties</propertyFile>
                            <promptOnNonLocalDatabase>false</promptOnNonLocalDatabase>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>mysql</groupId>
                                <artifactId>mysql-connector-java</artifactId>
                                <version>8.0.29</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>


</project>
