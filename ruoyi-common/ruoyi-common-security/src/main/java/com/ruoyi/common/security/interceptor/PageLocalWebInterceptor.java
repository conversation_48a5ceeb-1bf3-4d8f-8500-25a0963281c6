package com.ruoyi.common.security.interceptor;

import com.ruoyi.common.core.utils.PageUtils;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-06 13:42
 **/
public class PageLocalWebInterceptor implements AsyncHandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        PageUtils.clearPage();
        return true;
    }

}
