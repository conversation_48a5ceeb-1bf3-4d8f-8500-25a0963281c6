package com.ruoyi.common.security.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import com.ruoyi.common.core.exception.*;
import com.ruoyi.common.core.exception.auth.NotPermissionException;
import com.ruoyi.common.core.exception.auth.NotRoleException;
import com.ruoyi.common.core.exception.file.AmazonImageException;
import com.ruoyi.common.core.exception.file.FileException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.wrapper.RepeatedlyReadRequestWrapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * IllegalArgumentException
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({IllegalArgumentException.class})
    public AjaxResult badRequestException(IllegalArgumentException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        return defHandler(e.getMessage(), e, request, handlerMethod);
    }

    /**
     * HttpRequestMethodNotSupportedException
     * 返回状态码:405
     */
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public AjaxResult handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        return defHandler("不支持当前请求方法", e, request, handlerMethod);
    }


    /**
     * 权限码异常
     * 返回状态码:403
     */
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(NotPermissionException.class)
    public AjaxResult handleNotPermissionException(NotPermissionException e, HttpServletRequest request) {
        String reqUri = request.getRequestURI();
        log.error("请求地址'{}',权限码校验失败'{}'", reqUri, e.getMessage());
        return AjaxResult.error(HttpStatus.FORBIDDEN.value(), "没有访问权限，请联系管理员授权");
    }

    /**
     * 系统异常
     * 返回状态码:500
     */
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(SystemException.class)
    public AjaxResult systemException(SystemException e, HttpServletRequest request) {
        String reqUri = request.getRequestURI();
        log.error("请求地址'{}',系统异常'{}'", reqUri, e.getMessage());
        return AjaxResult.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "系统异常");
    }

    /**
     * 角色权限异常
     * 返回状态码:403
     */
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(NotRoleException.class)
    public AjaxResult handleNotRoleException(NotRoleException e, HttpServletRequest request) {
        String reqUri = request.getRequestURI();
        log.error("请求地址'{}',角色权限校验失败'{}'", reqUri, e.getMessage());
        return AjaxResult.error(HttpStatus.FORBIDDEN.value(), "没有访问权限，请联系管理员授权");
    }

    /**
     * 业务异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        log.error(e.getMessage(), e);
        return defHandler(e.getMessage(), e, request, handlerMethod);
    }

    /**
     * 拦截未知的运行时异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        return defHandler("未知异常", e, request, handlerMethod);
    }

    /**
     * 系统异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request, HandlerMethod handlerMethod) {
        String reqUri = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", reqUri, e);
        return defHandler("未知错误", e, request, handlerMethod);
    }

    /**
     * 自定义验证异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        log.error(e.getMessage(), e);
        List<String> errorMessage = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList());
        return defHandler(errorMessage.toString(), e, request, handlerMethod);
    }

    /**
     * 自定义验证异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        log.error(e.getMessage(), e);
        List<String> errorMessage = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList());
        return defHandler(errorMessage.toString(), e, request, handlerMethod);
    }

    /**
     * 内部认证异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(InnerAuthException.class)
    public AjaxResult handleInnerAuthException(InnerAuthException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        return defHandler(e.getMessage(), e, request, handlerMethod);
    }

    /**
     * 登录类型认证异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(LoginUserTypeException.class)
    public AjaxResult handleLoginUserTypeException(LoginUserTypeException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        return defHandler(e.getMessage(), e, request, handlerMethod);
    }


    /**
     * 文件服务异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(FileException.class)
    public AjaxResult handleServiceException(FileException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        log.error(e.getDefaultMessage(), e);
        return defHandler(e.getMessage(), e, request, handlerMethod);
    }

    /**
     * Amazon抓图异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(AmazonImageException.class)
    public AjaxResult handleAmazonException(AmazonImageException e, HttpServletRequest request) {
        log.info(e.getDefaultMessage(), e);
        return AjaxResult.success(e.getDefaultMessage());
    }

    /**
     * 非核心业务异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Biz200Exception.class)
    public AjaxResult handleBiz200Exception(Biz200Exception e) {
        log.info(e.getDefaultMessage(), e);
        if (Objects.equals(e.getType(), Biz200Exception.ERROR_IN_DATA)) {
            return AjaxResult.success("", e.getDefaultMessage());
        } else {
            return AjaxResult.success(e.getDefaultMessage());
        }
    }

    /**
     * 拦截参数校验不通过异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ConstraintViolationException.class)
    public AjaxResult handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        String reqUri = request.getRequestURI();
        log.error("请求地址'{}',参数校验不通过.", reqUri, e);

        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        if (CollUtil.isEmpty(violations)) {
            log.error("constraintViolationException violations 为空", e);
            return AjaxResult.error();
        }
        List<String> errorMessage = violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
        return defHandler(errorMessage.toString(), e, request, handlerMethod);
    }

    /**
     * 数据校验不通过异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(CheckedException.class)
    public AjaxResult handleCheckedException(CheckedException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        String reqUri = request.getRequestURI();
        log.error("请求地址'{}',数据校验不通过.", reqUri, e);
        if (Objects.equals(e.getType(), Biz200Exception.ERROR_IN_DATA)) {
            return AjaxResult.success("", e.getMessage());
        } else {
            return defHandler(e.getMessage(), e, request, handlerMethod);
        }
    }

    /**
     * 权限异常
     * 返回状态码:200
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(PreAuthorizeException.class)
    public AjaxResult handleCheckedException(PreAuthorizeException e, HttpServletRequest request, HandlerMethod handlerMethod) {
        String reqUri = request.getRequestURI();
        log.error("请求地址'{}',权限异常.", reqUri, e);
        return defHandler(e.getMessage(), e, request, handlerMethod);
    }


    private AjaxResult defHandler(String msg, Exception e, HttpServletRequest request, HandlerMethod handlerMethod) {
        detailLog(msg, e, request, handlerMethod);
        return AjaxResult.error(msg);
    }

    private void detailLog(String msg, Exception e, HttpServletRequest request, HandlerMethod handlerMethod) {
        StringBuilder errorData;
        errorData = StringUtils.isNotBlank(msg) ? new StringBuilder(msg) : new StringBuilder();
        if (SecurityUtils.getLoginUserType() != null) {
            errorData.append("\n用户类型:").append(SecurityUtils.getLoginUserType())
                    .append("\n用户名:").append(SecurityUtils.getLoginUser().getUsername());
        }
        errorData.append("\n请求方式:")
                .append(request.getMethod())
                .append("\n请求路径:")
                .append(request.getRequestURI())
                .append("\n客户端地址:")
                .append(IpUtils.getIpAddr(request))
                .append("\nUA:")
                .append(request.getHeader("User-Agent"));
        if (handlerMethod != null) {
            errorData.append("\n匹配类名:").append(handlerMethod);
        }
        StringBuilder data = new StringBuilder();
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (parameterMap != null && !parameterMap.isEmpty()) {
            parameterMap.forEach((key, value) -> data.append(key).append(StrPool.C_COLON).append(String.join(",", value)));
            data.append(StrPool.TAB);
        }
        try {
            if (request instanceof RepeatedlyReadRequestWrapper) {
                BufferedReader reader = request.getReader();
                if (reader != null) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        data.append(line);
                    }
                }
            }
            errorData.append("\n请求参数:").append(data);
        } catch (IOException ex) {
            errorData.append("\n捕获参数错误:").append(ex.getMessage());
        }
        log.warn(errorData.toString(), e);
    }
}
