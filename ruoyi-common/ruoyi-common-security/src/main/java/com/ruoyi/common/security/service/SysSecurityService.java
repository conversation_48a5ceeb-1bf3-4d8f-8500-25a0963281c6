package com.ruoyi.common.security.service;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.ConfigConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SysSecurityService {

    private final RedisService redisService;

    public SysSecurityService(RedisService redisService) {
        this.redisService = redisService;
    }

    public boolean isOpenSmsSecurity() {
        return ObjectUtil.equals(redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_SECURITY_SMS), Boolean.TRUE.toString());
    }

}
