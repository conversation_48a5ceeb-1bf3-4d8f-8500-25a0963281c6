package com.ruoyi.common.security.annotation;

import java.lang.annotation.*;

/**
 * 内部认证注解
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LoginUserType
{
    /**
     * 是否是主账号
     */
    boolean isOwnerAccount() default false;

    /**
     * 是否非mock数据（未绑定手机号）
     * @return
     */
    boolean isNoMock() default true;

    /**
     * 登录权限：运营端-0、商家端-1、模特端-2
     * @return
     */
    int[] userTypes();
}