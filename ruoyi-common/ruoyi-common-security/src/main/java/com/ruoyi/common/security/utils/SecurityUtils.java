package com.ruoyi.common.security.utils;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.enums.EventExecuteObjectEnum;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.ruoyi.system.api.model.LoginModel;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import javax.servlet.http.HttpServletRequest;

/**
 * 权限获取工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {
    /**
     * 获取用户ID
     */
    public static Long getUserId() {
        return SecurityContextHolder.getUserId();
    }
    public static Long getBizUserId() {
        return SecurityContextHolder.getBizUserId();
    }

    /**
     * 获取用户名称
     */
    public static String getUsername() {
        return SecurityContextHolder.getUserName();
    }

    /**
     * 获取用户key
     */
    public static String getUserKey() {
        return SecurityContextHolder.getUserKey();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginBaseEntity getLoginUser() {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginBaseEntity.class);
    }

    /**
     * 获取当前登录用户类型
     *
     * @see com.ruoyi.common.core.constant.UserTypeConstants
     */
    public static Integer getLoginUserType() {
        return SecurityContextHolder.get(SecurityConstants.TOKEN_TYPE, Integer.class);
    }

    /**
     * 获取登录用户信息
     */
    public static LoginBusiness getLoginBusinessUser() {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginBusiness.class);
    }

    /**
     * 获取请求token
     */
    public static String getToken() {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request) {
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token) {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId && UserTypeConstants.MANAGER.equals(getLoginUserType());
    }


    /**
     * 校验当前登录用户是否为管理员
     *
     * @return 结果
     */
    public static boolean currentUserIsAdmin() {
        return isAdmin(getUserId());
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 获取当前登录人信息
     * @return
     */
    public static LoginUserInfoVO getLoginUserInfoVo(){
        String SYSTEM_USER = "system";
        LoginUserInfoVO loginUserInfoVO = new LoginUserInfoVO();

        LoginBaseEntity loginUser = SecurityUtils.getLoginUser();
        if (loginUser instanceof LoginUser) {
            loginUserInfoVO.setPhone(((LoginUser) loginUser).getSysUser().getPhonenumber());
            loginUserInfoVO.setName(SecurityContextHolder.getUserName());
            loginUserInfoVO.setUserType(EventExecuteObjectEnum.BACK.getCode());
            loginUserInfoVO.setUserId(SecurityContextHolder.getUserId());
        } else if (loginUser instanceof LoginBusiness) {
            loginUserInfoVO.setUserType(EventExecuteObjectEnum.COMPANY.getCode());
            BusinessAccountVO businessAccountVO = ((LoginBusiness) loginUser).getBusinessAccountVO();
            loginUserInfoVO.setPhone(businessAccountVO.getPhone());
            loginUserInfoVO.setName(businessAccountVO.getName());
            loginUserInfoVO.setNickName(businessAccountVO.getNickName());
            loginUserInfoVO.setUserId(SecurityContextHolder.getUserId());

        } else if (loginUser instanceof LoginModel) {
            loginUserInfoVO.setName(SecurityContextHolder.getUserName());
            loginUserInfoVO.setUserType(EventExecuteObjectEnum.MODEL.getCode());
            loginUserInfoVO.setUserId(SecurityContextHolder.getUserId());
        } else {
            loginUserInfoVO.setUserType(EventExecuteObjectEnum.SYSTEM.getCode());
            loginUserInfoVO.setName(SYSTEM_USER);
        }

        return loginUserInfoVO;
    }

    public static String getCurrentOperateIp() {
        return SecurityContextHolder.get(SecurityConstants.CURRENT_IP, String.class);
    }
}
