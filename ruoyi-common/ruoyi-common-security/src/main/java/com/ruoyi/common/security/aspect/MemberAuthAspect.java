package com.ruoyi.common.security.aspect;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.annotation.MemberAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteBusinessAccountService;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 接口各个端口权限、是否主账号校验
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order
@AllArgsConstructor
public class MemberAuthAspect {
    private final RemoteBusinessAccountService remoteBusinessAccountService;

    @Around("@annotation(memberAuth)")
    public Object loginUserAround(ProceedingJoinPoint point, MemberAuth memberAuth) throws Throwable {
        BusinessAccountVO accountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        // 只有会员能够创建订单
        Assert.isTrue(StatusTypeEnum.YES.getCode().equals(accountVO.getBusinessVO().getMemberType()), "只有成为会员才能继续，请进入个人中心进行会员充值！");

        // 获取商家数据
        final BusinessAccountVO businessAccountVO = remoteBusinessAccountService.getAccountInfo(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getUnionid(), SecurityConstants.INNER);
        if (ObjectUtil.isNull(businessAccountVO)){
            throw new ServiceException("系统升级中，请稍后再试！");
        }
        if (UserStatus.DELETED.getCode().equals(String.valueOf(businessAccountVO.getUserStatus()))) {
            throw new ServiceException("对不起，您的账号：" + businessAccountVO.getAccount() + " 已被删除，如需恢复，请联系商家~");
        }
        Assert.isTrue(businessAccountVO.getUserStatus().compareTo(StatusEnum.UN_ENABLED.getCode()) != 0, "您的账户已被禁用!");

        return point.proceed();
    }
}
