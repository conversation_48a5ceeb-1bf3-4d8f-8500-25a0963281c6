package com.ruoyi.common.security.feign;

import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.domain.R;
import feign.FeignException;
import feign.Response;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-06 14:18
 **/
@Slf4j
public class FeignResultDecoder implements Decoder {
    private final SpringDecoder springDecoder;
    public FeignResultDecoder(SpringDecoder springDecoder){
        this.springDecoder = springDecoder;
    }
    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        if (response.status() != Constants.SUCCESS){
            throw new DecodeException(response.status(), "获取数据失败！", response.request());
        }
        if (response.body() == null) {
            throw new DecodeException(response.status(), "没有返回有效的数据", response.request());
        }
        if (response.headers().get(HttpHeaders.CONTENT_TYPE).contains(MediaType.APPLICATION_PDF_VALUE)){
            Response.Body body = response.body();
            try (InputStream inputStream = body.asInputStream()) {
                //获取byte数据
                return inputStream.readAllBytes();
            } catch (Exception e) {
                throw new DecodeException(R.FAIL, "获取流失败", response.request());
            }
        }
        Type[] actualTypeArgument = new Type[]{type};
        Type orginalType = this.getReference(actualTypeArgument).getType();

        R<?> result = (R<?>) springDecoder.decode(response, orginalType);
        if(R.FAIL == result.getCode()){
            throw new DecodeException(result.getCode(), result.getMsg() , response.request());
        }
        return result.getData();
    }

    private <T> ParameterizedTypeReference<R<T>> getReference(Type[] types) {
        ParameterizedTypeImpl parameterizedType = new ParameterizedTypeImpl( types, R.class.getDeclaringClass(), R.class);
        return ParameterizedTypeReference.forType(parameterizedType);
    }
}
