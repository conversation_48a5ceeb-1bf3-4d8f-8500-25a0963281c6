package com.ruoyi.common.security.service;

import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountTokenDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.ruoyi.system.api.model.LoginBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class TokenService {
    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final long expireTime = CacheConstants.EXPIRATION;

    private final long businessExpireTime = CacheConstants.BUSINESS_EXPIRATION;

    private final String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;

    private final String ACCOUNT_LOGIN_KEY = CacheConstants.ACCOUNT_LOGIN_KEY;

    private final Long ACCOUNT_LOGIN_MAX = 5L;

    private final Long MILLIS_MINUTE_TEN = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 创建令牌
     */
    public <T extends LoginBaseEntity> Map<String, Object> createToken(T loginUser) {
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        loginUser.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        loginUser.setUserAgent(ServletUtils.getRequest().getHeader("User-Agent"));
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.TOKEN_TYPE, loginUser.getUserType());
        claimsMap.put(SecurityConstants.USER_KEY, token);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, loginUser.getUserid());
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, loginUser.getUsername());

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", getExpireTime(loginUser));

        return rspMap;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginBaseEntity getLoginUser() {
        return getLoginUser(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginBaseEntity getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = SecurityUtils.getToken(request);
        return getLoginUser(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginBaseEntity getLoginUser(String token) {
        LoginBaseEntity user = null;
        try {
            if (StringUtils.isNotEmpty(token)) {
                String userkey = JwtUtils.getUserKey(token);
                user = redisService.getCacheObject(getTokenKey(userkey));
                return user;
            }
        } catch (Exception e) {
            log.info("getLoginUser获取用户信息失败{}", e.getMessage());
        }
        return user;
    }

    /**
     * 设置用户身份信息
     */
    public <T extends LoginBaseEntity> void setLoginUser(T loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userkey = JwtUtils.getUserKey(token);
            if (UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType())){
                String accountLoginKey = getLoginAccountKey(SecurityUtils.getBizUserId());
                redisService.removeZSet(accountLoginKey, getTokenKey(userkey));
            }
            redisService.deleteObject(getTokenKey(userkey));
        }
    }
    /**
     * 更新缓存数据
     */
    public void updateLoginBusiness(List<Long> bizUserIds, BusinessAccountTokenDTO dto) {

        if (StringUtils.isEmpty(bizUserIds)){
            return;
        }
        for (Long bizUserId : bizUserIds){
            //获取所有
            String accountLoginKey = getLoginAccountKey(bizUserId);
            Long aLong = redisService.zCard(accountLoginKey);
            if (StringUtils.isNull(aLong) || aLong.compareTo(0L) == 0){
                continue;
            }
            Set<String> userKeys = redisService.getZSet(accountLoginKey, 0, System.currentTimeMillis(), 0, aLong);
            if (StringUtils.isNotEmpty(userKeys)){
                for (String key : userKeys){
                    LoginBusiness user = redisService.getCacheObject(key);
                    if (StringUtils.isNotNull(user)){
                        BusinessAccountVO businessAccountVO = user.getBusinessAccountVO();
                        BusinessVO businessVO = businessAccountVO.getBusinessVO();
                        businessVO.setName(Optional.ofNullable(dto.getBusinessName()).orElse(businessVO.getName()));
                        businessVO.setScale(Optional.ofNullable(dto.getBusinessScale()).orElse(businessVO.getScale()));
                        businessVO.setIsProxy(Optional.ofNullable(dto.getIsProxy()).orElse(businessVO.getIsProxy()));
                        businessVO.setCustomerType(Optional.ofNullable(dto.getCustomerType()).orElse(businessVO.getCustomerType()));
                        businessVO.setBalance(Optional.ofNullable(dto.getBalance()).orElse(businessVO.getBalance()));
                        businessVO.setUseBalance(Optional.ofNullable(dto.getUseBalance()).orElse(businessVO.getUseBalance()));
                        businessVO.setValidBalance(Optional.ofNullable(dto.getBalance()).orElse(businessVO.getBalance()).subtract(Optional.ofNullable(dto.getUseBalance()).orElse(businessVO.getUseBalance())));
                        businessVO.setIsBalanceLock(Optional.ofNullable(dto.getIsBalanceLock()).orElse(businessVO.getIsBalanceLock()));
                        businessVO.setWaiterId(Optional.ofNullable(dto.getWaiterId()).orElse(businessVO.getWaiterId()));
                        businessVO.setMemberCode(Optional.ofNullable(dto.getMemberCode()).orElse(businessVO.getMemberCode()));
                        businessVO.setMemberType(Optional.ofNullable(dto.getMemberType()).orElse(businessVO.getMemberType()));
                        businessVO.setMemberStatus(Optional.ofNullable(dto.getMemberStatus()).orElse(businessVO.getMemberStatus()));
                        businessVO.setMemberPackageType(Optional.ofNullable(dto.getMemberPackageType()).orElse(businessVO.getMemberPackageType()));
                        businessVO.setMemberFirstTime(Optional.ofNullable(dto.getMemberFirstTime()).orElse(businessVO.getMemberFirstTime()));
                        businessVO.setMemberFirstType(Optional.ofNullable(dto.getMemberFirstType()).orElse(businessVO.getMemberFirstType()));
                        businessVO.setMemberLastTime(Optional.ofNullable(dto.getMemberLastTime()).orElse(businessVO.getMemberLastTime()));
                        businessVO.setMemberValidity(Optional.ofNullable(dto.getMemberValidity()).orElse(businessVO.getMemberValidity()));

                        businessAccountVO.setName(Optional.ofNullable(dto.getName()).orElse(businessAccountVO.getName()));
                        businessAccountVO.setNickName(Optional.ofNullable(dto.getNickName()).orElse(businessAccountVO.getNickName()));
                        businessAccountVO.setPic(Optional.ofNullable(dto.getPic()).orElse(businessAccountVO.getPic()));
                        businessAccountVO.setUnionid(Optional.ofNullable(dto.getUnionid()).orElse(businessAccountVO.getUnionid()));
                        businessAccountVO.setExternalUserId(Optional.ofNullable(dto.getExternalUserId()).orElse(businessAccountVO.getExternalUserId()));
                        businessAccountVO.setPhone(Optional.ofNullable(dto.getPhone()).orElse(businessAccountVO.getPhone()));
                        businessAccountVO.setBusinessVO(businessVO);
                        user.setUsername(Optional.ofNullable(dto.getName()).orElse(user.getUsername()));
                        user.setExpireTime(System.currentTimeMillis() + businessExpireTime * MILLIS_MINUTE);
                        redisService.setCacheObject(key, user, businessExpireTime, TimeUnit.MINUTES);
                    }
                }
            }
        }
    }

    /**
     * redis结构：
     * 登录账号-token关系
     *      key:bizUserId
     *          tokenA:
     *          tokenB:
     * token关系
     *      tokenA - 对应登录数据
     *      tokenB - 对应登录数据
     * @param bizUserIds
     */
    public void delAccessToken(List<Long> bizUserIds) {
        if (StringUtils.isEmpty(bizUserIds)){
            return;
        }
        for (Long bizUserId : bizUserIds){
            //获取所有
            String accountLoginKey = getLoginAccountKey(bizUserId);
            Long aLong = redisService.zCard(accountLoginKey);
            if (StringUtils.isNull(aLong) || aLong.compareTo(0L) == 0){
                continue;
            }
            Set<String> userKeys = redisService.getZSet(accountLoginKey, 0, System.currentTimeMillis(), 0, aLong);
            if (StringUtils.isNotEmpty(userKeys)){
                //删除登录账号下所有token
                for (String key : userKeys){
                    redisService.deleteObject(key);
                }
                //删除登录账号-token关系
                redisService.deleteObject(accountLoginKey);
            }
        }
    }

    public String getLoginAccountKey(Long bizUserId) {
        return ACCOUNT_LOGIN_KEY + bizUserId;
    }

    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param loginUser 登录信息
     */
    public <T extends LoginBaseEntity> void verifyToken(T loginUser) {
        if (UserTypeConstants.MODEL.equals(loginUser.getUserType())) {
            Assert.isNull(redisService.getCacheObject(CacheConstants.MODEL_LOGIN_LOCK_KEY + loginUser.getUserid()), "Account abnormality prevents login");
        }

        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public <T extends LoginBaseEntity> void refreshToken(T loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + getExpireTime(loginUser) * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisService.setCacheObject(userKey, loginUser, getExpireTime(loginUser), TimeUnit.MINUTES);
        if (UserTypeConstants.USER.equals(loginUser.getUserType())){
            String accountLoginKey = getLoginAccountKey(loginUser.getBizUserId());
            if (StringUtils.isNotNull(redisService.getZSetRank(accountLoginKey, userKey))){
                //更新数据
                redisService.setZSet(accountLoginKey, userKey, System.currentTimeMillis());
                return;
            }

            //存储商家账号与缓存关系  key = account,value = userKey
            Long aLong = redisService.zCard(accountLoginKey);
            if (aLong.compareTo(ACCOUNT_LOGIN_MAX) >= 0){
                Set<String> userKeys = redisService.getZSet(accountLoginKey, 0, System.currentTimeMillis(), 0, aLong.compareTo(ACCOUNT_LOGIN_MAX) + 1);
                for (String key : userKeys){
                    redisService.deleteObject(key);
                }
                //删除最早的数据
                redisService.removeRangeZSet(accountLoginKey, 0, aLong.compareTo(ACCOUNT_LOGIN_MAX));

            }
            redisService.setZSet(accountLoginKey, userKey, System.currentTimeMillis());
        }
    }


    private String getTokenKey(String token) {
        return ACCESS_TOKEN + token;
    }

    private <T extends LoginBaseEntity> Long getExpireTime(T loginUser){
        if (UserTypeConstants.USER.equals(loginUser.getUserType())){
            return businessExpireTime;
        }else{
            return expireTime;
        }
    }
}
