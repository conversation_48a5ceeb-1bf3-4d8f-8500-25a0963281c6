package com.ruoyi.common.security.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Aspect
@Component
@Slf4j
public class RequestBodyCollectionAspect {

    @Before("execution(* com..controller..*(..)) && " +
            "(@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PutMapping)) && " +
            "!@annotation(com.ruoyi.common.security.annotation.InnerAuth)")
    public void validateRequestBodyCollection(JoinPoint joinPoint) {
        log.debug("AOP切面拦截到请求: " + joinPoint.getSignature().getName());

        Object[] args = joinPoint.getArgs();

        for (Object arg : args) {
            // 检查参数是否为集合
            if (arg instanceof Collection) {
                Collection<?> collection = (Collection<?>) arg;
                if (collection.isEmpty()) {
                    log.warn("请求体集合为空");
                    throw new IllegalArgumentException("请求体缺失！");
                }
            }
        }
    }
}
