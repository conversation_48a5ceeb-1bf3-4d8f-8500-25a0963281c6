package com.ruoyi.common.security.aspect;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.exception.LoginUserTypeException;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * 接口各个端口权限、是否主账号校验
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order
public class LoginUserTypeAspect {

    @Around("@annotation(loginUserType)")
    public Object loginUserAround(ProceedingJoinPoint point, LoginUserType loginUserType) throws Throwable {
        //  用于内部服务调用忽略校验
        String source = ServletUtils.getRequest().getHeader(SecurityConstants.FROM_SOURCE);
        if (StrUtil.isNotBlank(source) && StringUtils.equals(SecurityConstants.INNER, source)) {
            return point.proceed();
        }

        int[] userTypes = loginUserType.userTypes();

        if (StringUtils.isNull(SecurityUtils.getLoginUserType())) {
            throw new LoginUserTypeException("登录用户类型校验失败！数据不能为空");
        }

        List<Integer> checkTypes = new ArrayList<>();
        if (StringUtils.isNotNull(userTypes) && userTypes.length > 0) {
            for (Integer userType : userTypes) {
                checkTypes.add(userType);
            }
        }

        //用户类型判断
        if (!checkTypes.contains(SecurityUtils.getLoginUserType())) {
            throwMyException(SecurityUtils.getLoginUserType());
        }
        //是否主账号判断
        if (loginUserType.isOwnerAccount()
                && UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType())
                && StatusTypeEnum.NO.getCode().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getIsOwnerAccount())
        ) {
            throwMyException(SecurityUtils.getLoginUserType());
        }

        if (loginUserType.isOwnerAccount()
                && loginUserType.isNoMock()
                && UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType())
                && StatusTypeEnum.YES.getCode().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getIsMock())
        ){
            //如果当前账号是mock数据 则无法操作
            throwMyException(SecurityUtils.getLoginUserType());
        }

        return point.proceed();
    }

    private void throwMyException(Integer userType) {
        if (UserTypeConstants.MODEL.equals(userType)){
            throw new LoginUserTypeException("Sorry, you don't have permission to access this resource.");
        }else {
            throw new LoginUserTypeException("抱歉，您没有权限访问该资源");
        }
    }
}
