package com.ruoyi.common.security.interceptor;

import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
public class HeaderInterceptor implements AsyncHandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // 自定义请求头，方便验证
        SecurityContextHolder.setRequestVersionHeader(ServletUtils.getHeader(request, CommonConstant.W_N_K_X_VERSION));

        String ipAddr = IpUtils.getIpAddr(request);
        if (StringUtils.isNotEmpty(ipAddr)) {
            SecurityContextHolder.setIpAddress(ipAddr);
        }

        String token = SecurityUtils.getToken();
        if (StringUtils.isNotEmpty(token)) {
            LoginBaseEntity loginUser = AuthUtil.getLoginUser(token);
            if (StringUtils.isNotNull(loginUser)) {
                AuthUtil.verifyLoginUserExpire(loginUser);
                // 额外存储用户类型，方便后期判断
                SecurityContextHolder.setTokenType(loginUser.getUserType());
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
                SecurityContextHolder.setUserId(Convert.toStr(loginUser.getUserid()));
                SecurityContextHolder.setBizUserId(loginUser.getBizUserId());
                SecurityContextHolder.setUserName(loginUser.getUsername());
                SecurityContextHolder.setUserKey(loginUser.getToken());
                SecurityContextHolder.setTokenType(loginUser.getUserType());
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        SecurityContextHolder.remove();
    }
}
