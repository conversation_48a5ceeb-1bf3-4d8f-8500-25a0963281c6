package com.wnkx.common.lb.config;


import com.ruoyi.common.core.constant.ConfigConstants;
import com.wnkx.common.lb.chooser.IRuleChooser;
import com.wnkx.common.lb.chooser.RoundRuleChooser;
import com.wnkx.common.lb.loadbalancer.VersionLoadBalancer;
import com.wnkx.common.lb.loadbalancer.VersionLoadBalancerLifecycle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.loadbalancer.LoadBalancerLifecycle;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.util.ClassUtils;

import java.lang.reflect.InvocationTargetException;

import static com.ruoyi.common.core.constant.ConfigConstants.CONFIG_LOADBALANCE_ISOLATION;

/**
 * 版本控制的路由选择类配置
 *
 * <AUTHOR>
 * @date 2024/05/23
 */
@Slf4j
public class VersionLoadBalancerConfig {

    @Bean
    @ConditionalOnMissingBean(IRuleChooser.class)
    @ConditionalOnProperty(prefix = CONFIG_LOADBALANCE_ISOLATION, value = "chooser")
    public IRuleChooser customRuleChooser(Environment environment, ApplicationContext context) {

        IRuleChooser chooser = new RoundRuleChooser();
        if (environment.containsProperty(ConfigConstants.CONFIG_LOADBALANCE_ISOLATION_CHOOSER)) {
            String chooserRuleClassString =
                environment.getProperty(ConfigConstants.CONFIG_LOADBALANCE_ISOLATION_CHOOSER);
            if (StringUtils.isNotBlank(chooserRuleClassString)) {
                try {
                    Class<?> ruleClass = ClassUtils.forName(chooserRuleClassString, context.getClassLoader());
                    chooser = (IRuleChooser) ruleClass.getDeclaredConstructor().newInstance();
                } catch (ClassNotFoundException e) {
                    log.error("没有找到定义的选择器，将使用内置的选择器", e);
                } catch (InstantiationException | IllegalAccessException e) {
                    log.error("没法创建定义的选择器，将使用内置的选择器", e);
                } catch (InvocationTargetException | NoSuchMethodException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return chooser;
    }

    @Bean
    @ConditionalOnMissingBean(value = IRuleChooser.class)
    public IRuleChooser defaultRuleChooser() {
        return new RoundRuleChooser();
    }


    @Bean
    @ConditionalOnProperty(prefix = "wnkx.loadbalance.isolation", name = "enabled", havingValue = "true")
    public ReactorServiceInstanceLoadBalancer versionServiceLoadBalancer(Environment environment,
                                                                         LoadBalancerClientFactory factory,
                                                                         IRuleChooser ruleChooser) {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
        String localIp = environment.getProperty(ConfigConstants.SPRING_CLOUD_CLIENT_IPADDRESS);
        return new VersionLoadBalancer(factory.getLazyProvider(name, ServiceInstanceListSupplier.class),
                name, localIp, ruleChooser);
    }

    @SuppressWarnings("rawtypes")
    @Bean
    @ConditionalOnProperty(prefix = CONFIG_LOADBALANCE_ISOLATION, name = "enabled", havingValue = "true")
    public LoadBalancerLifecycle loadBalancerLifecycle() {
        return new VersionLoadBalancerLifecycle();
    }
}
