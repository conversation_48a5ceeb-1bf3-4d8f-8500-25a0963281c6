package com.wnkx.common.lb.loadbalancer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.CommonConstant;
import com.wnkx.common.lb.chooser.IRuleChooser;
import com.wnkx.common.lb.utils.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.*;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义版本路由选择
 *
 * <AUTHOR>
 * @date 2024/05/23
 */
@Slf4j
public class VersionLoadBalancer implements ReactorServiceInstanceLoadBalancer {


    private final ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSuppliers;

    private final String serviceId;

    private final String serviceIp;

    private final IRuleChooser ruleChooser;

    public VersionLoadBalancer(ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSuppliers,
                               String serviceId,
                               String serviceIp,
                               IRuleChooser ruleChooser) {
        this.serviceInstanceListSuppliers = serviceInstanceListSuppliers;
        this.serviceId = serviceId;
        this.serviceIp = serviceIp;
        this.ruleChooser = ruleChooser;
    }

    @Override
    public Mono<Response<ServiceInstance>> choose(Request request) {
        // 从request中获取版本，兼容webflux方式
        RequestData requestData = ((RequestDataContext) (request.getContext())).getClientRequest();
        String version = getVersionFromRequestData(requestData);
        log.debug("选择的版本号为：{}", version);
        return Objects.requireNonNull(serviceInstanceListSuppliers.getIfAvailable())
                .get(request)
                .next()
                .map(instanceList -> getInstanceResponse(instanceList, version));
    }

    private String getVersionFromRequestData(RequestData requestData) {
        Map<String, String> queryMap = QueryUtils.getQueryMap(requestData.getUrl());
        if (MapUtils.isNotEmpty(queryMap) && queryMap.containsKey(CommonConstant.W_N_K_X_VERSION)
                && StringUtils.isNotBlank(queryMap.get(CommonConstant.W_N_K_X_VERSION))) {
            return queryMap.get(CommonConstant.W_N_K_X_VERSION);
        } else if (requestData.getHeaders().containsKey(CommonConstant.W_N_K_X_VERSION)) {
            List<String> versions = requestData.getHeaders().get(CommonConstant.W_N_K_X_VERSION);
            return versions != null ? versions.get(0) : null;
        }
        return null;
    }

    /**
     * 返回与版本号关联的实例.
     * </p>
     * 版本号为空时，优先本地实例，否则选择无版本号的实例。但是若版本号不为空，并且
     * 没有与版本号关联的实例，则会优先 fallback 到本地实例，无本地实例则 fallback 到无版本号的实例。
     * 若存在多个符合规则的实例，则通过 {@link IRuleChooser} 选取要使用的实例.
     *
     * @param instances 所有实例列表
     * @param version   版本号
     * @return 与版本号关联的实例，或 EmptyResponse
     */
    Response<ServiceInstance> getInstanceResponse(List<ServiceInstance> instances, String version) {
        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder();
            for (ServiceInstance instance : instances) {
                sb.append(instance.getHost());
                sb.append(":");
                sb.append(instance.getPort());
                sb.append(" ;");
            }
            log.debug("服务列表：{}", sb);
        }

        List<ServiceInstance> nonVersionedInstances = instances.stream()
                .filter(s -> !s.getMetadata().containsKey(CommonConstant.METADATA_VERSION))
                .collect(Collectors.toList());

        List<ServiceInstance> versionedInstances = instances.stream()
                .filter(instance -> instance.getMetadata().containsKey(CommonConstant.METADATA_VERSION))
                .collect(Collectors.toList());

//        优先级别
//        Spring serviceIp> NetUtil.getLocalhost()
        String host = StringUtils.isNotBlank(serviceIp) ?
                serviceIp : (Objects.nonNull(NetUtil.getLocalhost())
                ? ObjectUtil.defaultIfNull(NetUtil.getLocalhost().getHostAddress(), "") : "");
        List<ServiceInstance> hostInstances = instances.stream()
                .filter(instance -> host.equals(instance.getHost()))
                .collect(Collectors.toList());

        ServiceInstance serviceInstance;
        if (StringUtils.isBlank(version) && CollUtil.isEmpty(hostInstances)) {
            serviceInstance = this.ruleChooser.choose(nonVersionedInstances);
        } else if (StringUtils.isNotBlank(version)) {
            List<ServiceInstance> candidateInstances = versionedInstances.stream()
                    .filter(instance -> version.equals(instance.getMetadata().get(CommonConstant.METADATA_VERSION)))
                    .collect(Collectors.toList());
            if (candidateInstances.isEmpty()) {
                candidateInstances = CollUtil.isNotEmpty(hostInstances) ? hostInstances : nonVersionedInstances;
            }
            serviceInstance = this.ruleChooser.choose(candidateInstances);
        } else {
            serviceInstance = this.ruleChooser.choose(hostInstances);
        }

        if (!Objects.isNull(serviceInstance)) {
            log.debug("使用serviceId为：{}服务， 选择version为：{}， 地址：{}:{}，", serviceId, version,
                    serviceInstance.getHost(), serviceInstance.getPort());
            return new DefaultResponse(serviceInstance);
        }
        return new EmptyResponse();
    }
}
