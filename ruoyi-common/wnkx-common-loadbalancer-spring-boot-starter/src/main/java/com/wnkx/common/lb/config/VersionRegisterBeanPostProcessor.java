package com.wnkx.common.lb.config;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.common.utils.StringUtils;
import com.ruoyi.common.core.constant.CommonConstant;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static com.ruoyi.common.core.constant.ConfigConstants.CONFIG_LOADBALANCE_VERSION;

/**
 * 将版本注册到注册中心的组件
 *
 * <AUTHOR>
 * @date 2024/05/23
 */
public class VersionRegisterBeanPostProcessor implements BeanPostProcessor {
    private final String version;
    private static final String DEVELOPER_FILE = String.format("%s/wnkx/developer" , System.getProperty("user.home"));

    public VersionRegisterBeanPostProcessor(@Value("${" + CONFIG_LOADBALANCE_VERSION + ":}")
                                            String version) throws IOException {
        Path developerFilePath = Paths.get(DEVELOPER_FILE);
        if (Files.exists(developerFilePath)) {
            String developer = String.join("" , Files.readAllLines(developerFilePath));
            this.version = ObjectUtil.defaultIfBlank(developer, version);
        } else {
            this.version = version;
        }
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean,
                                                  String beanName) throws BeansException {
        if (bean instanceof NacosDiscoveryProperties && StringUtils.isNotBlank(version)) {
            NacosDiscoveryProperties nacosDiscoveryProperties = (NacosDiscoveryProperties) bean;
            nacosDiscoveryProperties.getMetadata().putIfAbsent(CommonConstant.METADATA_VERSION, version);
        }
        return bean;
    }
}
