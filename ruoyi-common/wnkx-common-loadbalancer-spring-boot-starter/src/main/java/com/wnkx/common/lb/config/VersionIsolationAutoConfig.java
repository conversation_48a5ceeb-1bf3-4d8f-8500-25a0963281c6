package com.wnkx.common.lb.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.context.annotation.Import;


/**
 * 版本隔离自动配置
 *
 * <AUTHOR>
 * @date 2024/05/23
 */
@LoadBalancerClients(defaultConfiguration = VersionLoadBalancerConfig.class)
@ConditionalOnProperty(prefix = "wnkx.loadbalance.isolation" , name = "enabled" , havingValue = "true")
@Import({VersionRegisterBeanPostProcessor.class})
public class VersionIsolationAutoConfig {
}
